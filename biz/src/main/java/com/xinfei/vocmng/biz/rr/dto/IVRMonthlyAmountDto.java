/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * IVR当月应还金额信息DTO
 *
 * <AUTHOR>
 * @version $ IVRMonthlyAmountDto, v 0.1 2025-06-18 shaohui.chen Exp $
 */
@Data
public class IVRMonthlyAmountDto {

    @ApiModelProperty(value = "用户当月总应还金额（包含逾期金额）")
    private BigDecimal totalMonthlyAmount;

    @ApiModelProperty(value = "是否存在减免方案")
    private Boolean hasReductionPlan;

    @ApiModelProperty(value = "减免方案笔数")
    private Integer planCount;

    @ApiModelProperty(value = "有减免方案的借据总应还金额")
    private BigDecimal planTotalAmount;

    @ApiModelProperty(value = "最早一笔方案到期时间")
    private LocalDate earliestPlanDueDate;
}
