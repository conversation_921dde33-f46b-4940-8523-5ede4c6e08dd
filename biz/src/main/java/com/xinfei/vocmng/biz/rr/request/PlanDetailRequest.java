/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @version $ PlanDetailRequest, v 0.1 2024-03-28 22:15 junjie.yan Exp $
 */
@Data
public class PlanDetailRequest {

    @ApiModelProperty("方案编号")
    @NotNull
    private Long planId;

    @ApiModelProperty("是否触发聚合支付/短信")
    private Boolean isApplyAggregate;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("用户号")
    private String userNo;

    @ApiModelProperty("客户号")
    private String custNo;

}