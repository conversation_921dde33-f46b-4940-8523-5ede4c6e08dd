/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/6/24 11:23
 * CardRefund
 */
@Data
public class CardRefund {
    @ApiModelProperty("会员卡id：订单id")
    private Long vipCardId;

    @ApiModelProperty("会员卡类型：1黑卡，2会员卡，3：飞享会员卡 4:飞跃会员")
    private Integer vipCardType;

    @ApiModelProperty("退款流水号")
    private String refundFlowNumber;

    @ApiModelProperty("退款金额：元")
    private BigDecimal amount;

    @ApiModelProperty("退款状态 0:初始化退款中，1:退款成功，2:退款失败")
    private Integer status;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("退款原因")
    private String reason;

    @ApiModelProperty("创建时间,申请退款时间")
    private LocalDateTime createTime;

    @ApiModelProperty("订单号")
    private String orderNumber;

    @ApiModelProperty("退款账号")
    private String refundAccount;

    @ApiModelProperty("退款账号类型：1，银行卡，2：支付宝")
    private Integer refundAccountType;

    @ApiModelProperty("是否为申请记录")
    private Boolean isRefundApply = false;

    @ApiModelProperty("申请id")
    private Long applyId;

    @ApiModelProperty("退款银行")
    private String refundBankName;

    @ApiModelProperty("退款申请类型： 1：原路退回  2，线下")
    private Integer refundApplyChannel;

    @ApiModelProperty("更新时间,退款成功或失败时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("渠道")
    private String channelCode;

    @ApiModelProperty("三方渠道流水号")
    private String channelOrderNo;

    private String applyFlowNo;

    @ApiModelProperty("是否可退款+撤销")
    private Boolean isRefundCancel = false;

    @ApiModelProperty("退款失败原因")
    private String refundFailReason;

    @ApiModelProperty("退款申请创建时间")
    private LocalDateTime refundApplyCreateTime;
}