/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RepaymentProcessReq, v 0.1 2024-03-29 17:18 junjie.yan Exp $
 */
@Data
public class RefundFeeRatioProcessReq {

    @ApiModelProperty("借据号")
    @NotBlank(message = "loanNo不能为空")
    private String loanNo;

    @ApiModelProperty("目标费率")
    private BigDecimal targetFeeRatio;

    @ApiModelProperty("期望退款金额")
    private BigDecimal targetRefundAmt;
}