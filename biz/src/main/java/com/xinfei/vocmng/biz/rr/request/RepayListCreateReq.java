/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/11/7 17:44
 * RepayListReq
 */
@Data
public class RepayListCreateReq {

    @ApiModelProperty("custNo")
    @NotNull(message = "custNo必传")
    private String custNo;

    @ApiModelProperty("是否永久关闭")
    private Boolean isForeverClose;

    @ApiModelProperty("操作人")
    @NotNull(message = "操作人必传")
    private String operatedBy;

    @ApiModelProperty("关闭天数")
    private Long closeDay;

    @ApiModelProperty("关闭原因")
    private String closeDayReason;

    @ApiModelProperty("ENABLE 启用, DISABLE 禁用")
    @NotNull(message = "status必传")
    private String status;
}