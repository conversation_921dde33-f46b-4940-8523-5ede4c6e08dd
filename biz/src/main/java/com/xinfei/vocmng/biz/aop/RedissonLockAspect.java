/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.aop;

/**
 * <AUTHOR>
 * @version $ RedissonLockAspect, v 0.1 2024/3/28 19:39 wancheng.qu Exp $
 */

import com.xinfei.vocmng.biz.model.annotation.RedissonLockAnnotation;
import com.xinfei.vocmng.biz.util.RedissonLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
@Slf4j
public class RedissonLockAspect {

    @Pointcut("@annotation(com.xinfei.vocmng.biz.model.annotation.RedissonLockAnnotation)")
    public void redissonLockPoint() {
    }

    @Around("redissonLockPoint()")
    public Object checkLock(ProceedingJoinPoint pjp) throws Throwable {
        String threadName = Thread.currentThread().getName();
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        String methodName = signature.getName();
        log.info("thread:{} thread come redissonlock,method:{}", threadName, methodName);
        RedissonLockAnnotation annotation = ((MethodSignature) pjp.getSignature()).
                getMethod().getAnnotation(RedissonLockAnnotation.class);
        String lockRedisKey = annotation.lockRedisKey();
        if (StringUtils.isBlank(lockRedisKey)) {
            log.info("thread:{} thread come but lock name is null,method:{}", threadName, methodName);
            return pjp.proceed();
        } else {
            if (RedissonLockUtils.tryLock(lockRedisKey)) {
                try {
                    log.info("thread :{} get lock success,method:{}", threadName, methodName);
                    return pjp.proceed();
                } finally {
                    if (RedissonLockUtils.isLocked(lockRedisKey)) {
                        if (RedissonLockUtils.isHeldByCurrentThread(lockRedisKey)) {
                            RedissonLockUtils.unlock(lockRedisKey);
                            log.info("thread:{} lock is delete success,lockKey=:{}", threadName, lockRedisKey);
                        }
                    }
                }
            } else {
                log.info("thread :{} get lock fail,Method does not execute,method:{}", threadName, methodName);
                return null;
            }
        }

    }
}