package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SmsRecordDetail, v 0.1 2023/12/27 09:33 qu.lu Exp $
 */
@Data
public class SmsRecordsDto {
    /** 触发时间 */
    @ApiModelProperty("触发时间")
    private String createdTime;

    /** 来源 */
    @ApiModelProperty("来源")
    private String tplBizType;

    /** 模板：主题内容 */
    @ApiModelProperty("主题内容")
    private String templateDesc;

    /** 短信内容 */
    @ApiModelProperty("短信内容")
    private String content;

    /** 客户号码 */
    @ApiModelProperty("客户号码")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    /** 触达状态 */
    @ApiModelProperty("触达状态")
    private String deliveryStatus;

    /** 发送通道 */
    @ApiModelProperty("发送通道")
    private String agent;
}
