/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> 2024/7/16 下午1:51
 * RepayReductionDto
 */

@Data
public class RepayReductionDto {
    @ApiModelProperty("抵扣详情id")
    private String reductionDetailId;

    @ApiModelProperty("抵扣来源借据号")
    private String loanNo;

    @ApiModelProperty("抵扣入账借据号")
    private String toLoanNo;

    @ApiModelProperty("抵扣总金额")
    private BigDecimal reductionTotalAmount;

    @ApiModelProperty("抵扣本金")
    private BigDecimal reductionPrin;

    @ApiModelProperty("抵扣利息")
    private BigDecimal reductionInt;

    @ApiModelProperty("担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("逾期费")
    private BigDecimal overdueFee;

    @ApiModelProperty("提前结清手续费")
    private BigDecimal reductionFee4;

    @ApiModelProperty("状态 (SUCCESS成功,CANCEL撤销)")
    private String status;

    @ApiModelProperty("抵扣时间")
    private LocalDateTime reductionTime;

    @ApiModelProperty("操作人")
    private String createdBy;
}