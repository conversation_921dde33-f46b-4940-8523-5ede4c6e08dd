/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ PlanDetailDto, v 0.1 2024-03-30 19:40 junjie.yan Exp $
 */

@Data
public class HuttaPlanDetailDto {
    @ApiModelProperty("方案明细编号")
    private String planDetailId;

    @ApiModelProperty("还款类型：1：还当期，2：提前结清")
    private Integer planType;

    @ApiModelProperty("方案状态:（0：待生效、1：生效中 、2：失效、3：成功）")
    private Integer planStatus;

    @ApiModelProperty("方案有效期")
    private LocalDateTime endTime;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty("总减免金额")
    private BigDecimal totalReduct;

    @ApiModelProperty("实际需还")
    private BigDecimal realAmt;

    @ApiModelProperty("状态,0-失效,1-有效")
    private Integer status;

    @ApiModelProperty("使用状态,0-未使用,1-部分使用,2-已使用")
    private Integer useStatus;

    @ApiModelProperty("减免规则 锁定减免金额:DEDUCT_AMT_LIMIT 锁定应还金额:PAY_AMT_LIMIT")
    private String deductRule;

    @ApiModelProperty("手机号（聚合支付/线下还款使用）")
    private String mobile;

    @ApiModelProperty(value = "银行卡列表(在线抵扣使用)")
    private List<BankDto> banks;

    @ApiModelProperty("是否包含按日计息订单")
    private Boolean hasDailyType;

    @ApiModelProperty("客户号")
    private String custNo;

    @ApiModelProperty("用户号")
    private Long userNo;

    @ApiModelProperty(value = "总期数")
    private Integer totalTerms;

    @ApiModelProperty("是否为JD渠道限制划扣方式")
    private Boolean isJDWithhold;

    @ApiModelProperty("是否为反诈渠道限制")
    private Boolean isAntiFraud;

    @ApiModelProperty("是否为回购渠道限制")
    private Boolean isBuyBack;
}