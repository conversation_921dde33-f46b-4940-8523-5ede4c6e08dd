package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR> 2024/6/21 10:48
 * RightCardRefundRecordDto
 */
@Data
public class RightCardRefundDto {

    @ApiModelProperty("可申请金额：元")
    private BigDecimal remainRefundAmount;

    @ApiModelProperty("可申请金额下限：元")
    private BigDecimal refundAmountMin;

    @ApiModelProperty("可申请金额上限：元")
    private BigDecimal refundAmountMax;

    @ApiModelProperty("该坐席是否可退")
    private Boolean isRefund;

    @ApiModelProperty("支付订单号")
    private String orderNo;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("会员卡id：黑卡，会员卡，飞享会员卡订单id cardId")
    private Long vipCardId;

    @ApiModelProperty("卡类型:1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡")
    private Integer cardType;

}