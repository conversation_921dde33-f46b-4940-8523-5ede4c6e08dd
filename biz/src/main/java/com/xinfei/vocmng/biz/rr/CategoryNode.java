/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr;

import lombok.Data;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ CategoryNode, v 0.1 2025-04-09 15:02 junjie.yan Exp $
 */
@Data
public class CategoryNode {
    private Long categoryId;
    private int count1;  // dataList1 的数量
    private int count2;  // dataList2 的数量
    private Map<String, Integer> hourlyCountNow = new LinkedHashMap<>(); // 当天小时统计
    private Map<String, Integer> hourlyCountPeriod = new LinkedHashMap<>(); // 对比时段小时统计
    private Map<Long, CategoryNode> subCategories;

    public CategoryNode(Long categoryId) {
        this.categoryId = categoryId;
        this.subCategories = new HashMap<>();
    }

    // 增加 count1 的方法
    public void incrementCount1() {
        this.count1++;
    }

    // 增加 count2 的方法
    public void incrementCount2() {
        this.count2++;
    }

}