/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2024/7/4 14:36
 */
@Data
public class LogOffReq {

    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("原因")
    private String remark;

    @ApiModelProperty("false-延迟注销 true-立即注销")
    private Boolean immediate;

    @ApiModelProperty("app")
    @NotBlank(message = "app不能为空")
    private String app;

    @ApiModelProperty("操作人")
    @NotBlank(message = "operatorName不能为空")
    private String operatorName;

    @ApiModelProperty("手机号")
    @NotBlank(message = "mobile不能为空")
    private String mobile;
}