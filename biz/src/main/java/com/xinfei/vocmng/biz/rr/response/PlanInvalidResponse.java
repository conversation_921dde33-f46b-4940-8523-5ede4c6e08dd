/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ PlanInvalidResponse, v 0.1 2024-07-17 12:02 junjie.yan Exp $
 */
@Data
public class PlanInvalidResponse {
    @ApiModelProperty("客服方案编号")
    private Long planId;

    @ApiModelProperty("催收方案明细编号")
    private String planDetailId;

    @ApiModelProperty("结果")
    private Boolean isSuccess;

    @ApiModelProperty("原因")
    private String message;
}