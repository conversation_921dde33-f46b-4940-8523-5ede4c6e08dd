/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ BankFlowRequest, v 0.1 2024-05-11 15:42 junjie.yan Exp $
 */
@Data
public class BankFlowRequest extends PageRequestDto {
    @ApiModelProperty(value = "开始日期")
    @NotNull(message = "开始日期不能为空")
    private String startDate;

    @ApiModelProperty(value = "结束日期")
    @NotNull(message = "结束日期不能为空")
    private String endDate;

    @ApiModelProperty(value = "还款方式,支付宝:zfb,银行卡:bank")
    private List<String> channelCode;

    @ApiModelProperty(value = "退款类型 01-线下退款，02-溢缴款退款")
    private List<String> typeList;

    @ApiModelProperty(value = "交易流水号")
    private String transNo;

    @ApiModelProperty(value = "入账金额开始")
    private Long startAmount;

    @ApiModelProperty(value = "入账金额结束")
    private Long endAmount;

}