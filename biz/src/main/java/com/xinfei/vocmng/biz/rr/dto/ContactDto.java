/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ ContactDto, v 0.1 2024-01-02 18:02 junjie.yan Exp $
 */
@Data
public class ContactDto {
    @ApiModelProperty(value = "客户号")
    private String custNo;

    @ApiModelProperty(value = "用户号")
    private Long userNo;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "联系人类型")
    private String contactType;

    @ApiModelProperty(value = "联系人类型名称")
    private String contactTypeName;

    @ApiModelProperty(value = "联系人姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String contactName;

    @ApiModelProperty(value = "联系人手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String contactMobileNo;

    @ApiModelProperty(value = "联系人序号(contactType相同时的联系人排序)")
    private Integer sortNo;

    @ApiModelProperty(value = "数据是否删除(0正常，1删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "老业务表id(mongodb表id)")
    private String oldId;
}