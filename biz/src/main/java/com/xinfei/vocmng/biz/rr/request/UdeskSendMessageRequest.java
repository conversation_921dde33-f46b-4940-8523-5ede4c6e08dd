package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.xfframework.common.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ UdeskSendMessageRequest, v 0.1 2025/7/9 17:22 shaohui.chen Exp $
 */
@Data
public class UdeskSendMessageRequest extends BaseRequest {

    @ApiModelProperty(value = "短信模板id")
    @NotBlank
    private String templateId;

    private Map<String, Object> data;

    @ApiModelProperty(value = "mobile")
    private String mobile;

    @ApiModelProperty(value = "app")
    @NotBlank
    private String app = "xyf01";

}
