package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 添加订单备注请求参数
 *
 * <AUTHOR>
 * @version $ AddOrderRemarkRequest, v 0.1 2024/1/13 11:23 qu.lu Exp $
 */
@Data
public class AddOrderRemarkRequest {
    @ApiModelProperty("订单号")
    @NotNull(message = "orderNo不能为空")
    private String orderNo;
    @ApiModelProperty("备注信息")
    @NotNull(message = "remark不能为空")
    private String remark;
}
