/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.google.gson.Gson;
import com.xinfei.vocmng.biz.remote.MemberInterestRemoteService;
import com.xinfei.vocmng.biz.rr.request.RefundResultMsg;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2024/9/30 10:34
 * 会员卡退款失败创建工单
 */
@Component
@RocketMQMessageListener(consumerGroup = "cg_vocmng_tp_vipcore_refund_result", topic = "tp_vipcore_refund_result")
@Slf4j
public class VipRefundConsumer extends AbstractMessageListener<String> {

    @Autowired
    private MemberInterestRemoteService memberInterestRemoteService;

    @Override
    protected void execute(String body) {
        RefundResultMsg t = new Gson().fromJson(body, RefundResultMsg.class);
        if (t != null && t.getStatus() != null && t.getStatus() == 2) {
            memberInterestRemoteService.refundVipFailed(t, null);
        }
    }
}