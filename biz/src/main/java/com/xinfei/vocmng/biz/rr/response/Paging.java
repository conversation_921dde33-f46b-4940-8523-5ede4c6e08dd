/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页返回结果
 *
 * <AUTHOR>
 * @version $ NameTypeInfoReq, v 0.1 2023/9/22 12:49 PM longjie.yuan Exp $
 */
@Data
public class Paging<T> {

    protected List<T> list;
    protected long currentPage;
    protected long pageSize;
    private long totalPage;
    private long total;

    public Paging() {
    }

    public Paging(long current, long pageSize) {
        this.list = new ArrayList<>();
        this.currentPage = current;
        this.pageSize = pageSize;
        this.totalPage = 0L;
        this.total = 0L;
    }

    public Paging(List<T> dataList, long current, long pageSize, long total) {
        this.list = dataList;
        this.currentPage = current;
        this.pageSize = pageSize;
        this.total = total;
        this.totalPage = total % pageSize == 0 ? total / pageSize : total / pageSize + 1;
    }

    public Paging(List<T> dataList, long current, long pageSize, long total, long pages) {
        this.list = dataList;
        this.currentPage = current;
        this.pageSize = pageSize;
        this.total = total;
        this.totalPage = pages;
    }

    public Paging(long currentPage, long pageSize, List<T> sourceList) {
        if (sourceList == null || sourceList.isEmpty()){
            return;
        }

        // 总记录条数
        this.total = sourceList.size();

        if (pageSize <= 0) {
            pageSize = 10;
        }
        // 每页显示多少条记录
        this.pageSize = pageSize;

        // 获取总页数
        this.totalPage = this.total / this.pageSize;
        if (this.total % this.pageSize != 0)
            this.totalPage = this.totalPage + 1;

        // 当前第几页数据
        this.currentPage = Math.min(this.totalPage, currentPage);

        // 起始索引
        long fromIndex = this.pageSize * (this.currentPage - 1);

        // 结束索引
        long toIndex = Math.min(this.pageSize * this.currentPage, this.total);
        this.list = sourceList.subList((int) fromIndex, (int) toIndex);
    }
}
