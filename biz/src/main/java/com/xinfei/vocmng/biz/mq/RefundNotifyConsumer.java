/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.google.gson.Gson;
import com.xinfei.repaytrade.facade.rr.message.RefundNotifyMsg;
import com.xinfei.vocmng.biz.model.enums.RefundInstructionStatusEnum;
import com.xinfei.vocmng.biz.service.RefundService;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ RefundNotifyConsumer, v 0.1 2024/3/7 17:25 wancheng.qu Exp $
 */
@Component
@RocketMQMessageListener(consumerGroup = "cg_vocmng_tp_repaytrade_refund_notify", topic = "tp_repaytrade_refund_notify")
@Slf4j
public class RefundNotifyConsumer extends AbstractMessageListener<String> {

    @Autowired
    private RefundService refundService;

    @Override
    protected void execute(String body) {
        RefundNotifyMsg t = new Gson().fromJson(body, RefundNotifyMsg.class);
        if (t != null && StringUtils.isNotEmpty(t.getRefundInstructionStatus()) && RefundInstructionStatusEnum.isFailed(t.getRefundInstructionStatus())) {
            refundService.refundFailed(t);
        }
    }
}