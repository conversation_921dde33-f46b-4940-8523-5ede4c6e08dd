package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ RightUseDetail, v 0.1 2024/1/11 13:25 qu.lu Exp $
 */
@Data
public class RightUseDetailDto {
    @ApiModelProperty("权益明细ID")
    private String id;
    @ApiModelProperty("权益明细")
    private String name;
    @ApiModelProperty("使用条件/平台")
    private String subName;
    @ApiModelProperty("")
    private Integer type;
    @ApiModelProperty("")
    private String img;
    @ApiModelProperty("面额")
    private String price;
    @ApiModelProperty("")
    private String triggerType;
    @ApiModelProperty("权益领取状态：false未发放  true已发放")
    private Boolean isGet;
    @ApiModelProperty("权益领取时间")
    private String getAt;
    @ApiModelProperty("权益使用状态：false未使用 true已使用")
    private Boolean isUsed;
    @ApiModelProperty("权益使用时间")
    private String usedAt;
}
