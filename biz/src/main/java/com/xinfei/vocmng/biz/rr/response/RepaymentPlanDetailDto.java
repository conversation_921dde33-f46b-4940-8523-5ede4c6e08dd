/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentPlanResp, v 0.1 2024-04-02 10:46 junjie.yan Exp $
 */
@Data
public class RepaymentPlanDetailDto {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty("本金减免金额")
    private BigDecimal prinReduct;

    @ApiModelProperty("利息减免金额")
    private BigDecimal intReduct;

    @ApiModelProperty("担保费减免金额")
    private BigDecimal guarantReduct;

    @ApiModelProperty("逾期费减免金额")
    private BigDecimal lateReduct;

    @ApiModelProperty("挡板减免金额")
    private BigDecimal redReduct;

    @ApiModelProperty("总减免金额")
    private BigDecimal totalReduct;

    @ApiModelProperty("实际需还")
    private BigDecimal realAmt;

    @ApiModelProperty("原来应还")
    private BigDecimal oldRealAmt;

    @ApiModelProperty("抵扣金额")
    private BigDecimal deduction;

    @ApiModelProperty("状态,0-失效,1-有效")
    private Integer status;

    @ApiModelProperty("还款方式")
    private Integer repayMethod;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty(value = "总期数")
    private Integer totalTerms;

    @ApiModelProperty("使用状态,0-未使用,1-部分使用,2-已使用")
    private Integer useStatus;

    @ApiModelProperty("是否为JD渠道限制划扣方式")
    private Boolean isJDWithhold;

    @ApiModelProperty("是否为反诈渠道限制")
    private Boolean isAntiFraud;

    @ApiModelProperty("是否为回购渠道限制")
    private Boolean isBuyBack;

}