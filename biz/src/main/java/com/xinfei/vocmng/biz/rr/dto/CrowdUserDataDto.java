package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 人群用户数据DTO
 *
 * <AUTHOR>
 * @version $ CrowdUserDataDto, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Data
public class CrowdUserDataDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private String userNo;

    @ApiModelProperty(value = "工单编号列表")
    private List<Long> workOrderNoList;

    @ApiModelProperty(value = "飞跃会员订单号")
    private Long vipOrderNo;

    @ApiModelProperty(value = "退款类型：1-退卡退款，2-取消扣款")
    private Integer refundType;
}
