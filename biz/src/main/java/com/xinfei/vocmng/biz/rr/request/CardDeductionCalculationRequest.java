/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version $ CardDeductionCalculationRequest, v 0.1 2025-06-24 16:32 pengming.liu Exp $
 */
@Data
public class CardDeductionCalculationRequest {

    @ApiModelProperty("费率rate")
    @NotNull(message = "费率rate不为空")
    private BigDecimal rate;

    @ApiModelProperty("金额")
    @NotNull(message = "amount不为空")
    private BigDecimal amount;

}