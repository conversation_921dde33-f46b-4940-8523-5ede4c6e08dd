package com.xinfei.vocmng.biz.aop;

import com.xinfei.vocmng.biz.component.remote.RemoteProcessStrategy;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/12/14
 */
@Configuration
public class FeignInterceptor implements RequestInterceptor {
    @Autowired
    private RemoteProcessStrategy remoteProcessStrategy;

    @Override
    public void apply(RequestTemplate template) {
        String appName = template.feignTarget().name();
        remoteProcessStrategy.doProcess(appName,template);
    }
}
