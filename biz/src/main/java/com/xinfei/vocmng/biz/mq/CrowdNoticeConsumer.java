/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.google.gson.Gson;
import com.xinfei.vocmng.biz.rr.request.CrowdNoticeMsg;
import com.xinfei.vocmng.biz.service.CrowdNoticeService;
import com.xinfei.vocmng.itl.constants.MQConstants;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 人群通知消息消费者
 *
 * <AUTHOR>
 * @version $ CrowdNoticeConsumer, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Component
@RocketMQMessageListener(
        consumerGroup = MQConstants.DATA_INSIGHT.CG_DATAINSIGHT_CROWD_NOTICE,
        topic = MQConstants.DATA_INSIGHT.TP_DATAINSIGHT_CROWD_NOTICE
)
@Slf4j
public class CrowdNoticeConsumer extends AbstractMessageListener<CrowdNoticeMsg> {

    @Autowired
    private CrowdNoticeService crowdNoticeService;

    @Override
    protected void execute(CrowdNoticeMsg crowdNoticeMsg) {
        crowdNoticeService.processCrowdNotice(crowdNoticeMsg);
    }
}
