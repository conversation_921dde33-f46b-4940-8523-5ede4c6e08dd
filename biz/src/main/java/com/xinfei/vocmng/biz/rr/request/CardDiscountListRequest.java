/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ RepayPlanListReq, v 0.1 2024-03-28 21:42 junjie.yan Exp $
 */
@Data
public class CardDiscountListRequest extends PageRequestDto {

    @ApiModelProperty("方案编号")
    private Long planId;

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty("custNo")
    private String custNo;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("会员卡id")
    private Long cardId;

    @ApiModelProperty("会员卡订单号")
    private String orderNo;

    @ApiModelProperty("方案状态:0：待生效、1：生效中 、2：失效、3：成功")
    private Integer planStatus;

    @ApiModelProperty("创建时间起始")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8" // 建议加上时区，避免时区问题
    )
    private LocalDateTime createdTimeStart;

    @ApiModelProperty("创建时间结束")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8" // 建议加上时区，避免时区问题
    )
    private LocalDateTime createdTimeEnd;

}