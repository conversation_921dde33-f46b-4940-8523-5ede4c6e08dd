package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员卡权益使用明细信息
 *
 * <AUTHOR>
 * @version $ MemberCardUseInfoDto, v 0.1 2023/12/25 20:47 qu.lu Exp $
 */
@Data
public class MemberCardUseInfoDto {
    @ApiModelProperty("权益名称")
    private String name;
    @ApiModelProperty("可领取次数")
    private Integer limitNum;
    @ApiModelProperty("领取次数")
    private Integer num;
    @ApiModelProperty("是否可展示")
    private Boolean canShow;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("权益类型")
    private String itemTypeName;


    @ApiModelProperty("商品id")
    private Integer itemId;
    @ApiModelProperty("领取时间")
    private String createTime;
    @ApiModelProperty("价值:分")
    private String price;
    @ApiModelProperty("类型：1:领取， 2:兑换")
    private String type;
    @ApiModelProperty("领取明细")
    List<ReceiveDetailsDto> receiveDetailList;

    @ApiModelProperty("商品价值汇总")
    BigDecimal itemPriceSum;

    @ApiModelProperty("商品成本汇总")
    BigDecimal itemCostSum;
}
