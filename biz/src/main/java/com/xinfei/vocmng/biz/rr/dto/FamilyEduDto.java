/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ FamilyEduDto, v 0.1 2024-01-02 18:01 junjie.yan Exp $
 */
@Data
public class FamilyEduDto {
    @ApiModelProperty(value = "客户号")
    private String custNo;

    @ApiModelProperty(value = "用户号")
    private Long userNo;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "婚姻状况")
    private String marriageStatus;

    @ApiModelProperty(value = "家庭住址")
    @DataPermission(type = DataPermissionType.MASK_ADDRESS)
    private String address;

    @ApiModelProperty(value = "家庭住址邮编")
    private String districtCode;

    @ApiModelProperty(value = "学历")
    private String education;

    @ApiModelProperty(value = "数据是否删除(0正常，1删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "老业务表id(mongodb表id)")
    private String oldId;
}