/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2025/03/20 14:52
 * OrderFilterFactorDto
 */
@Data
public class OrderFilterFactorUnencryptedDto {

    @ApiModelProperty("订单size")
    private Integer orderSize;

    @ApiModelProperty("资金池List")
    private List<String>  capitalPoolList;

    @ApiModelProperty("innerAppList")
    private List<String> innerAppList;

    @ApiModelProperty("资金池加密对照map")
    private Map<String,String> capitalPoolEncrypt;

}