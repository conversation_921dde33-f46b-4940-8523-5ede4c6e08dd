/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * IVR近期账单信息DTO
 *
 * <AUTHOR>
 * @version $ IVRLatestBillDto, v 0.1 2025-06-18 shaohui.chen Exp $
 */
@Data
public class IVRLatestBillDto {

    @ApiModelProperty(value = "用户最近一笔信贷订单账单日")
    private LocalDate latestBillDate;

    @ApiModelProperty(value = "用户最近一笔信贷订单应还金额")
    private BigDecimal latestOrderAmount;

    @ApiModelProperty(value = "用户所有逾期金额")
    private BigDecimal totalOverdueAmount;

    @ApiModelProperty(value = "是否存在减免方案")
    private Boolean hasReductionPlan;

    @ApiModelProperty(value = "减免方案笔数")
    private Integer planCount;

    @ApiModelProperty(value = "有减免方案的借据总应还金额")
    private BigDecimal planTotalAmount;

    @ApiModelProperty(value = "最早一笔方案到期时间")
    private LocalDate earliestPlanDueDate;
}
