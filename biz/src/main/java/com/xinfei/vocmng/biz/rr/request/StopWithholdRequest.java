package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/8/21 14:34
 *
 */
@Data
public class StopWithholdRequest {
    @ApiModelProperty("会员订单id")
    @NotNull(message = "cardId会员订单id不为空")
    private Long cardId;

    @ApiModelProperty("会员卡类型：1老会员卡  2新会员卡 3飞享会员 4飞跃会员")
    @NotNull(message = "会员卡类型不能为空")
    private Integer type;

    @ApiModelProperty("飞享/飞跃会员状态")
    @NotBlank(message = "飞享/飞跃会员状态不能为空")
    private String vipStatus;
}
