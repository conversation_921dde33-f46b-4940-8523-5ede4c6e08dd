/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ ResignContractReq, v 0.1 2024/8/27 15:32 wancheng.qu Exp $
 */
@Data
public class ResignContractReq implements Serializable {


    @NotBlank(message = "type不能为空")
    private String type;
    @NotBlank(message = "协议key不能为空")
    private String shortName;
    private String orderNumber;
    private String contractId;
    private String data;
    private String fixData;
    @AssertTrue(message = "订单号不能为空")
    public boolean isOrderNumberValid() {
        if ("cash".equals(type)) {
            return orderNumber != null && !orderNumber.isEmpty();
        }
        return true;
    }

    @AssertTrue(message = "合同ID不能为空")
    public boolean isContractIdValid() {
        if ("consume".equals(type) || "activation".equals(type) || "bind_card".equals(type)) {
            return contractId != null && !contractId.isEmpty();
        }
        return true;
    }
}