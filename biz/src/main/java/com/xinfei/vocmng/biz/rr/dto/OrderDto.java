/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class OrderDto {

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime dateCreated;

    @ApiModelProperty("资金放款申请流水号")
    private String fundLoanApplySerialNo;

    @ApiModelProperty(value = "资方订单号——(根据fundLoanApplySerialNo查询)")
    private String outOrderNumber;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty("银行卡id")
    private String bankcardId;

    @ApiModelProperty("入账银行名称")
    private String bankName;

    @ApiModelProperty("入账银行卡号")
    @DataPermission(type = DataPermissionType.MASK_CARDNO)
    private String bankcardNo;

    @ApiModelProperty("用户号")
    private String userNo;

    @ApiModelProperty("客户号")
    private String custNo;

    @ApiModelProperty("产品编码")
    private String subProductCode;

    @ApiModelProperty("合同编号")
    private String contractNumber;

    @ApiModelProperty(value = "渠道方订单号——(根据loanReqNo查询)")
    private String channelOrderNumber;

    @ApiModelProperty(value = "借据号")
    private String loanNo;

    @ApiModelProperty("关联的信贷订单号(现金订单号)")
    private String mainLoanReqNo;

    @ApiModelProperty("营收订单号")
    private String profitLoanReqNo;

    @ApiModelProperty(value = "订单状态:INIT(00, 待审核)," +
            "RISK_AUDITING(01, 风控审核中)," +
            "RISK_AUDIT_PASS(11, 风控审核通过)," +
            "FUND_LENDING(20, 放款处理中)," +
            "SUCCESS(03, 交易成功)," +
            "FAIL(04, 交易失败);")
    private String status;

    @ApiModelProperty("资方名称")
    private String orgName;

    @ApiModelProperty("资方编码")
    private String fundSource;

    @ApiModelProperty("资金池——(orgName + fundSource)")
    @DataPermission(type = DataPermissionType.MASK_MONEY)
    private String capitalPool;

    @ApiModelProperty(value = "到账时间")
    private LocalDateTime dateCash;

    @ApiModelProperty(value = "借款金额")
    private BigDecimal loanAmount;

    @ApiModelProperty(value = "借款期数")
    private Integer term;

    @ApiModelProperty(value = "注册APP")
    private String app;

    @ApiModelProperty(value = "失败原因")
    private String failedReason;

    @ApiModelProperty(value = "最后结清时间——(LCS)")
    private LocalDateTime dateSettle;

    @ApiModelProperty("借据完整类型,COMPLETE: 借据完整, MISS_DEDUCT:缺失减免")
    private String completeType;

    @ApiModelProperty(value = "借据状态:RP-正常, OD-逾期, FP-结清——(LCS)")
    private String loanStatus;

    @ApiModelProperty("利息计息方式，TERMLY:按月(期)利率 DAILY: 按日利率——(LCS)")
    private String intCalType;

    @ApiModelProperty("服务类型")
    private String serviceType;

    @ApiModelProperty("借款用途")
    private String loanPurpose;

    @ApiModelProperty("来源app")
    private String innerApp;

    @ApiModelProperty("渠道app")
    private String utmSource;

    @ApiModelProperty("营收产品名称")
    private String subProductName;

    @ApiModelProperty("利率，等额本金为日利率, 等本等息为年利率——(LCS)")
    private BigDecimal feeRate;

    @ApiModelProperty("有效方案来源(vocmng:客服 HUTTA:催收)")
    private String planSource;

    @ApiModelProperty("子订单列表（现金订单存在）")
    private List<OrderDto> subOrders;

    @ApiModelProperty("优惠劵id列表")
    private List<Long> couponIdList;

    @ApiModelProperty("是否可以取消额度")
    private Boolean isLogoutQuota;

    @ApiModelProperty(value = "最近账单日(取最早一期未还账单的账单日，若无则不返回)")
    private LocalDate dateDue;

    @ApiModelProperty(value = "现金订单本金")
    private BigDecimal mainOrderPrinAmt;

    @ApiModelProperty(value = "预借款订单号")
    private String preLoanInfo;

    @ApiModelProperty(value = "资方借据号")
    private String thirdLoanNo;

}