/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version $ GetOrderListRequest, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */

@Data
public class GetOrderDetailRequest {

    @ApiModelProperty("订单类型列表，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    @NotBlank
    private String orderType;

    @ApiModelProperty(value = "订单号")
    @NotBlank
    private String orderNo;

}