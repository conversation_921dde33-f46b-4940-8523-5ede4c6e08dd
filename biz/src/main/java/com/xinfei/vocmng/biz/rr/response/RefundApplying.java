/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ RefundApplying, v 0.1 2024-06-22 18:39 junjie.yan Exp $
 */
@Data
public class RefundApplying {
    @ApiModelProperty("自增主键：applyId")
    private Long id;

    @ApiModelProperty("会员卡订单号 黑卡：无， 会员卡：有，飞向会员卡：有 （支付单号）")
    private String vipOrderNumber;

    @ApiModelProperty("退款流水号")
    private String refundFlowNumber;

    @ApiModelProperty("用户号")
    private String userNo;

    @ApiModelProperty("退款金额：元")
    private BigDecimal amount;

    @ApiModelProperty("会员卡id")
    private Long vipCardId;

    @ApiModelProperty("卡类型:1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡")
    private Integer cardType;

    @ApiModelProperty("退卡类型，1:退款，2:退卡&退款")
    private Integer refundType;

    @ApiModelProperty("线下退款账号")
    private String offlineAccount;

    @ApiModelProperty("线下退款账号类型：1:银行卡，2:支付宝")
    private Integer offlineAccountType;

    @ApiModelProperty("1申请通过（可撤销）:，2:审核通过（不可撤销），3:处理中，4:处理陈工，5:处理失败,6手动关闭")
    private Integer status;

    @ApiModelProperty("操作人")
    private String operator;

    @ApiModelProperty("退款流程信息")
    private String message;

    @ApiModelProperty("是否删除 0:未删除，1已删除")
    private Integer isDeleted;

    @ApiModelProperty("自动处理时间（状态从 申请通过 转为 审核通过）")
    private LocalDateTime autoProcessTime;

    @ApiModelProperty("创建时间）")
    private LocalDateTime createTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("退款备注")
    private String reason;

}