/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * IVR订单信息DTO
 *
 * <AUTHOR>
 * @version $ UserStatusRequest, v 0.1 2025/5/12 16:30 shaohui.chen Exp $
 */
@Data
public class IVROrderInfoDto {

    @ApiModelProperty(value = "用户最近一笔信贷订单账单日")
    private LocalDate latestBillDate;

    @ApiModelProperty(value = "用户最近一笔信贷订单应还金额")
    private BigDecimal latestOrderAmount;

    @ApiModelProperty(value = "用户当月总应还金额（包含逾期金额）")
    private BigDecimal totalMonthlyAmount;

    @ApiModelProperty(value = "用户所有逾期金额")
    private BigDecimal totalOverdueAmount;
}
