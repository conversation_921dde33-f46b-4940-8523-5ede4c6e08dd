/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> 2025/03/20 16:52
 * OrderFilterFactorRequest
 */

@Data
public class OrderFilterFactorRequest {

    @ApiModelProperty(value = "custNo")
    @NotBlank(message = "custNo不能为空")
    private String custNo;

}