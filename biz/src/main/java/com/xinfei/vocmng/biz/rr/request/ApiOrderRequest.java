/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2024/8/28 10:14
 * ApiOrderRequest
 */
@Data
public class ApiOrderRequest {

    @ApiModelProperty("app")
    private String app;

    @ApiModelProperty("mobile")
    private String mobile;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("产品编号")
    private String productNo;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("分页大小")
    private String pageSize;

    @ApiModelProperty("当前页")
    private String currentPage;
}