/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.aop;

import com.xinfei.ssocore.client.SsoClient;
import com.xinfei.ssocore.client.component.SsoUserDataUtil;
import com.xinfei.ssocore.client.component.SsoUserInfoDTO;
import com.xinfei.vocmng.biz.config.VocConfig;
import com.xinfei.vocmng.biz.model.annotation.ResourcePermission;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.exception.TechplayException;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.biz.util.JwtUtil;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ LoginRequiredAspect, v 0.1 2023/12/23 16:32 wancheng.qu Exp $
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class LoginRequiredAspect {

    private final HttpServletRequest request;
    private final LoginService loginService;
    private final VocConfig vocConfig;
    private final SsoUserDataUtil ssoUserDataUtil;
    private final SsoClient ssoClient;

    @Autowired
    public LoginRequiredAspect(HttpServletRequest request, LoginService loginService, VocConfig vocConfig,SsoUserDataUtil ssoUserDataUtil,SsoClient ssoClient) {
        this.request = request;
        this.loginService = loginService;
        this.vocConfig = vocConfig;
        this.ssoUserDataUtil = ssoUserDataUtil;
        this.ssoClient = ssoClient;
    }

    @Pointcut("@within(com.xinfei.vocmng.biz.model.annotation.LoginRequired) || @annotation(com.xinfei.vocmng.biz.model.annotation.LoginRequired)")
    public void loginRequired() {
    }

    @Pointcut("@annotation(com.xinfei.vocmng.biz.model.annotation.ResourcePermission) && (@within(com.xinfei.vocmng.biz.model.annotation.LoginRequired) || @annotation(com.xinfei.vocmng.biz.model.annotation.LoginRequired))")
    public void resourcePermissionAndLoginRequired() {
    }


    @Before("loginRequired()")
    public void checkLogin(JoinPoint joinPoint) {
        UserInfo userInfo = vocConfig.isSsoOpenFlag()? getSsoUser():parseJwtFromHeader();
        UserContextHolder.setUserContext(userInfo);
    }

    private UserInfo getSsoUser() {
        try {
            ssoClient.checkLogin();
            SsoUserInfoDTO userInfo = ssoUserDataUtil.getUserInfo();
            log.info("sso getUserInfo res:{}", JsonUtil.toJson(userInfo));
            String identify = userInfo.getOldUserIdDTO().getVocUserId();
            UserInfo user = loginService.getUserInfo(identify);
            if (Objects.isNull(user) || Objects.equals(1, user.getState())) {
                throw new IgnoreException(TechplayErrDtlEnum.USERCLOSE_ERROR);
            }
            return user;
        } catch (Exception e) {
            log.warn("getSsoUser error", e);
            throw new IgnoreException(TechplayErrDtlEnum.SSO_LOGIN_ERROR);
        }

    }

    @Before("resourcePermissionAndLoginRequired()")
    public void checkResourcePermission(JoinPoint joinPoint) {
        UserInfo user = UserContextHolder.getUserContext();
        if (Objects.isNull(user) || StringUtils.isBlank(user.getUserIdentify())) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGIN_NATIVE_CACHE_ERROR);
        }

        UserInfo userInfo = loginService.getUserInfo(user.getUserIdentify());
        if (Objects.equals("root", userInfo.getRole())) {
            //管理员不用校验
            return;
        }
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ResourcePermission resourcePermission = method.getAnnotation(ResourcePermission.class);

        checkResourcePermissionForUser(userInfo, resourcePermission);

    }

    private void checkResourcePermissionForUser(UserInfo userInfo, ResourcePermission resourcePermission) {
        List<String> resourceAuth = userInfo.getResourceAuth();
        String value = resourcePermission.value();

        checkValueNotBlank(value);

        if (CollectionUtils.isEmpty(resourceAuth) || !resourceAuth.contains(value)) {
            throw new IgnoreException(TechplayErrDtlEnum.NO_RESOURCE_PERMISSION_ERROR);
        }
    }

    private void checkValueNotBlank(String value) {
        if (StringUtils.isBlank(value)) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGIN_TOKEN_VAIN_ERROR);
        }
    }

    @After("loginRequired()")
    public void clearThreadLocal() {
        UserContextHolder.clearUserContext();
    }


    private UserInfo parseJwtFromHeader() {
       /* RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();

        if (requestAttributes instanceof ServletRequestAttributes) {

            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();*/

        String jwtToken = request.getHeader("token");

        if (StringUtils.isBlank(jwtToken)) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGIN_NOTOKEN_ERROR);
        }
        String identify = JwtUtil.getUserIdentifyFromToken(jwtToken,loginService);
        if (StringUtils.isBlank(identify)) {
            throw new IgnoreException(TechplayErrDtlEnum.LOGIN_TOKEN_VAIN_ERROR);
        }
        UserInfo userInfo = loginService.getUserInfo(identify);
        if(Objects.isNull(userInfo) || Objects.equals(1,userInfo.getState())){
            throw new IgnoreException(TechplayErrDtlEnum.USERCLOSE_ERROR);
        }
        return userInfo;

        // throw new TechplayException(TechplayErrDtlEnum.LOGIN_NOTOKEN_ERROR);
    }


}
