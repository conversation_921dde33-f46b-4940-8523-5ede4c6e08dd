/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ FeeAmountDtoResp, v 0.1 2025-02-26 15:26 junjie.yan Exp $
 */
@Data

public class FeeAmountDtoResp {

    @ApiModelProperty("本金")
    private BigDecimal transPrin;

    @ApiModelProperty("利息")
    private BigDecimal transInt;

    @ApiModelProperty("罚息")
    private BigDecimal transOint;

    @ApiModelProperty("逾期管理费")
    private BigDecimal transFee1;

    @ApiModelProperty("平台服务费")
    private BigDecimal transFee2;

    @ApiModelProperty("贷后逾期管理费")
    private BigDecimal transFee3;

    @ApiModelProperty("提前还款手续费")
    private BigDecimal transFee4;

    @ApiModelProperty("还款违约金")
    private BigDecimal transFee5;

    @ApiModelProperty("还款滞纳金")
    private BigDecimal transFee6;

    @ApiModelProperty("voc担保费=担保费+反担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("voc逾期费=罚息+罚息（我方）+催费")
    private BigDecimal lateFee;

}