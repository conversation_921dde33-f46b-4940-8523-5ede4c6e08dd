/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ BillPlanDeductDto, v 0.1 2024-03-11 14:10 junjie.yan Exp $
 */
@Data
public class PlanDeductDto {

    @ApiModelProperty("减免金额总和")
    private BigDecimal deductSum;

    @ApiModelProperty("应减免金额")
    private BigDecimal deductAmt;

    @ApiModelProperty("非营销应减免金额")
    private BigDecimal unprofitDeductAmt;

    @ApiModelProperty("实际减免金额")
    private BigDecimal actDeductAmt;

    @ApiModelProperty("非营销实际减免金额")
    private BigDecimal actUnprofitDeductAmt;

    @ApiModelProperty("减免明细-总")
    private PlanDetailDto deductPlan;

    @ApiModelProperty("减免明细-实际发生")
    private PlanDetailDto actDeductPlan;

    @ApiModelProperty("减免明细-细分")
    private List<DeductDetailDto> deductPlanList;
}