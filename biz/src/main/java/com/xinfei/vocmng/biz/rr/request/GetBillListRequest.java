/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.xfframework.common.BaseDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ GetOrderListRequest, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */

@Data
public class GetBillListRequest extends BaseDto {

    @ApiModelProperty(value = "借据号")
    private List<String> loanNos;

    @ApiModelProperty(value = "借据号:对应订单类型(PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）)")
    private Map<String, String> loanNoOrderTypes;

    @ApiModelProperty(value = "PayPlanDomain:账单状态:0-未到期,1-逾期,2-本期结清")
    private List<String> rpyFlags;

    @ApiModelProperty(value = "PayPlanDomain:账单日（到期日）起始")
    private LocalDate dateDueStart;

    @ApiModelProperty(value = "PayPlanDomain:账单日（到期日）结束")
    private LocalDate dateDueEnd;

    @ApiModelProperty(value = "账单号勾选")
    private  Map<String,List<String>> planNos;
}