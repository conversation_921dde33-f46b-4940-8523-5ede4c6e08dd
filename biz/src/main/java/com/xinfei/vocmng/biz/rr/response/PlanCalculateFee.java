/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 * @version $ PlanCalculateFee, v 0.1 2024-08-20 14:38 junjie.yan Exp $
 */
@Data
public class PlanCalculateFee {

    @ApiModelProperty("账单号")
    private String billNo;

    @ApiModelProperty(value = "账单状态:0-未到期,1-逾期,2-本期结清")
    private String rpyFlag;

    @ApiModelProperty("期数")
    private Integer term;

    @ApiModelProperty(value = "账单日（到期日）")
    private LocalDate dateDue;

    @ApiModelProperty("计划本金")
    private BigDecimal planPrinAmt;

    @ApiModelProperty("计划利息")
    private BigDecimal planIntAmt;

    @ApiModelProperty("计划担保费")
    private BigDecimal planGuaranteeFee;

    @ApiModelProperty("计划逾期费")
    private BigDecimal planLateFee;

    @ApiModelProperty("计划应还总额")
    private BigDecimal planTotalAmt;

}