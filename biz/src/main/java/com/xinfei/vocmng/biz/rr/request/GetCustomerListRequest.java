/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version $ GetOrderListRequest, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class GetCustomerListRequest extends PageQuery {

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "手机号查CustNo")
    private String mobileCust;

    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "用户状态 0：注销  10：正常 20：临时注销 2：注销中")
    private Integer status;

    @ApiModelProperty(value = "注册APP")
    private String registerApp;

    @ApiModelProperty(value = "来源APP")
    private String sourceChannel;
}