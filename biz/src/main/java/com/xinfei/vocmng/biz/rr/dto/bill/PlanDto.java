/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ OrderDto, v 0.1 2023-12-19 14:39 junjie.yan Exp $
 */
@Data
public class PlanDto {

    @ApiModelProperty(value = "期数")
    private Integer term;

    @ApiModelProperty(value = "账单状态:0-未到期,1-逾期,2-本期结清")
    private String rpyFlag;

    @ApiModelProperty(value = "账单号（还款计划编号）")
    private String planNo;

    @ApiModelProperty(value = "创建日期")
    private LocalDateTime dateCreated;

    @ApiModelProperty(value = "账单日（到期日）")
    private LocalDate dateDue;

    @ApiModelProperty("当期起息日")
    private LocalDate dateStart;

    @ApiModelProperty("逾期天数")
    private Integer overdueDays;

    @ApiModelProperty(value = "容时期限（宽限日）")
    private LocalDate dateGrace;

    @ApiModelProperty("代偿类型：0 未代偿, 1 发生代偿且代偿罚息, 2 发生代偿不代偿罚息'")
    private String compensatedType;

    @ApiModelProperty(value = "结清时间（结清日期）")
    private LocalDateTime dateSettle;

    @ApiModelProperty(value = "费项明细")
    private PlanFeeDetailDto planFeeDetail;

}