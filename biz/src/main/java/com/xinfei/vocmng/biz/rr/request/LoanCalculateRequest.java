/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class LoanCalculateRequest {

    @ApiModelProperty("订单信息列表")
    @NotEmpty(message = "订单信息列表不能为空")
    @Valid
    private List<LoanInfo> loanInfos;
}