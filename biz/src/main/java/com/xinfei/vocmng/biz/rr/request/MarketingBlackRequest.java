/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> 2024/10/25 14:15
 * MarketingBlackRequest
 */
@Data
public class MarketingBlackRequest implements Serializable {

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "APP")
    private String app;

    @ApiModelProperty(value = "主标签")
    private String mainLabelKey;

    @ApiModelProperty(value = "子标签")
    private String subLabelKey;

    @ApiModelProperty(value = "加黑天数")
    private Long day;

    @ApiModelProperty(value = "创建人")
    private String operator;

    @ApiModelProperty(value = "来源 1：新标签系统 2：老黑名单系统")
    private String source;
}