/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RepayFeeDetailDto, v 0.1 2024-04-11 14:01 junjie.yan Exp $
 */
@Data
public class RepayFeeDetailDto {
    @ApiModelProperty("本金")
    private BigDecimal transPrin;

    @ApiModelProperty("利息")
    private BigDecimal transInt;

    @ApiModelProperty("罚息")
    private BigDecimal transOint;

    @ApiModelProperty("担保费（贷后管理费）")
    private BigDecimal transFee1;

    @ApiModelProperty("反担保费（平台服务费）")
    private BigDecimal transFee2;

    @ApiModelProperty("罚息我方（贷后逾期管理费）")
    private BigDecimal transFee3;

    @ApiModelProperty("提前结清手续费")
    private BigDecimal transFee4;

    @ApiModelProperty("违约金")
    private BigDecimal transFee5;

    @ApiModelProperty("催费")
    private BigDecimal transFee6;

    @ApiModelProperty("voc担保费=担保费+反担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("voc逾期费=罚息+罚息（我方）+催费")
    private BigDecimal lateFee;

}