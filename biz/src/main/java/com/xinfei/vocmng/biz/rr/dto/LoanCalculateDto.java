/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ OfflineWriteOffDto, v 0.1 2024-05-11 17:14 junjie.yan Exp $
 */
@Data
public class LoanCalculateDto {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty(value = "账单状态:0-未到期,1-逾期,2-本期结清")
    private String rpyFlag;

    @ApiModelProperty("还款类型：1：还多期，2：提前结清")
    private Integer planType;

    @ApiModelProperty("总减免金额")
    private BigDecimal totalReduct;

    @ApiModelProperty("抵扣金额")
    private BigDecimal deduction;

    @ApiModelProperty("实际应还金额")
    private BigDecimal realAmt;

    @ApiModelProperty("原来应还金额")
    private BigDecimal oldRealAmt;

    @ApiModelProperty("方案状态 0：待生效、1：生效中 、2：失效、3：成功")
    private Integer planStatus;

    @ApiModelProperty("有效方案来源(vocmng:客服 HUTTA:催收)")
    private String planSource;

    @ApiModelProperty("客服方案编号")
    private Long id;

    @ApiModelProperty("催收方案明细编号")
    private String planDetailId;

    @ApiModelProperty("是否为回购渠道限制")
    private Boolean isBuyBack;

}