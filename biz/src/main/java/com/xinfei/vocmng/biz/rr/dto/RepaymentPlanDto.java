package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.rr.response.RepaymentPlanDetailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 还款方案
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27 10:27:00
 */

@Data
public class RepaymentPlanDto {

    @ApiModelProperty("还款方案编号")
    private Long id;

    @ApiModelProperty("减免方案明细编号")
    private String planDetailId;

    @ApiModelProperty("custNo")
    private String custNo;

    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("减免金额总和")
    private BigDecimal exemptionAmt;

    @ApiModelProperty("抵扣金额")
    private BigDecimal deductionAmt;

    @ApiModelProperty("订单号")
    private List<String> orderNos;

    @ApiModelProperty("借据号")
    private List<String> loanNos;

    @ApiModelProperty("借据号订单号映射")
    private Map<String, String> loanOrderNos;

    @ApiModelProperty("借据号与期数映射")
    private Map<String, Integer> loanNoTerms;

    @ApiModelProperty("方案状态")
    private Integer planStatus;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String updater;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("方案结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("方案类型：1：还当期，2：提前结清")
    private Integer planType;

    @ApiModelProperty("有效方案来源")
    private String planSource;

    @ApiModelProperty("手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty("方案明细")
    private List<RepaymentPlanDetailDto> repaymentPlanDetails;

    @ApiModelProperty("审批状态:（0：待审批、1：审批通过 、2：审批拒绝）")
    private Integer reviewStatus;

    @ApiModelProperty("方案类型")
    private String solutionType;
}
