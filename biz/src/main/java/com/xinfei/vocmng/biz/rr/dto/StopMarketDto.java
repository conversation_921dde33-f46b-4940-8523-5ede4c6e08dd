/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ StopMarketDto, v 0.1 2024/8/16 15:22 wancheng.qu Exp $
 */
@Data
public class StopMarketDto implements Serializable {

    @ApiModelProperty(value = "序号")
    private String id;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    private String mobileMd5;
    @ApiModelProperty(value = "app")
    private String app;
    @ApiModelProperty(value = "状态")
    private String status;
    private String createdTime;
    @ApiModelProperty(value = "更新时间")
    private String updatedTime;
    private String enableMarketTime;
    private String disableMarketTime;
    @ApiModelProperty(value = "加黑原因")
    private String disableReason;
    private String operatorId;
    @ApiModelProperty(value = "操作人")
    private String operator;
}