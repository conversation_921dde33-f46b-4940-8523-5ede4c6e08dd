/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ DeductionInfoDto, v 0.1 2024-01-25 10:52 junjie.yan Exp $
 */
@Data
public class DeductionInfoDto {

    @ApiModelProperty("扣款单号")
    private String deductionNo;

    @ApiModelProperty("扣款发起时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("扣款交易时间")
    private LocalDateTime dateTran;

    @ApiModelProperty("交易总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("真实扣款金额明细")
    private RepayFeeDetailDto realAmountDetail;

    @ApiModelProperty("扣款成功金额")
    private BigDecimal amount;

    @ApiModelProperty("扣款结果")
    private String status;

    @ApiModelProperty("结果说明")
    private String failedReason;

    @ApiModelProperty("扣款渠道")
    private String channelCode;

    @ApiModelProperty("是否是冲销记录")
    private Boolean isCX;

    @ApiModelProperty("扣款单减总金额")
    private BigDecimal deductTotalAmt;

    @ApiModelProperty("减免方案id")
    private String planDetailId;

    @ApiModelProperty("是否卡单")
    private String isCardOrder;

}