/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 2024/8/12 18:08
 * 用户优惠券列表Req
 */
@Data
public class UserCouponReq extends PageQuery {

    @ApiModelProperty("userNo")
    @NotNull(message = "userNo必传")
    private Long userNo;

    @ApiModelProperty("0:未使用 1:已使用 2:失效")
    private Integer status;

}