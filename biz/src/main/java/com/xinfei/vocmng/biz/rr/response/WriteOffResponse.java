/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class WriteOffResponse {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty("结果")
    private Boolean isSuccess;

    @ApiModelProperty("原因")
    private String message;
}