/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.aop;

import com.xinfei.vocmng.biz.component.DictDataCache;
import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import com.xinfei.vocmng.biz.model.enums.TechplayErrDtlEnum;
import com.xinfei.vocmng.biz.model.enums.VipPayTypeEnum;
import com.xinfei.vocmng.biz.model.exception.IgnoreException;
import com.xinfei.vocmng.biz.model.resp.ApiResponse;
import com.xinfei.vocmng.biz.model.resp.UserInfo;
import com.xinfei.vocmng.biz.rr.dto.VipOrderPayLogDetailDTO;
import com.xinfei.vocmng.biz.service.LoginService;
import com.xinfei.vocmng.biz.util.DataMaskingUtil;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.DictDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Objects;


/**
 * 数据权限，限定接口返回类型为ApiResponse
 * 方法和返回值字段标识权限切入注解
 */
@Slf4j
@Aspect
@Component
@Order(2)
public class DataPermissionAspect {

    private final LoginService loginService;
    private final DictDataCache dictDataCache;

    @Autowired
    public DataPermissionAspect(LoginService loginService,DictDataCache dictDataCache) {
        this.loginService = loginService;
        this.dictDataCache=dictDataCache;
    }

    @Pointcut("@annotation(com.xinfei.vocmng.biz.model.annotation.DataPermission)")
    public void dataPermission() {
    }

    @AfterReturning(value = "@annotation(dataPermission)", returning = "returnValue")
    public Object applyDataPermission(JoinPoint joinPoint, DataPermission dataPermission, Object returnValue) {
        if (Objects.nonNull(returnValue)) {
            try {
                UserInfo user = UserContextHolder.getUserContext();
                if (user == null || StringUtils.isBlank(user.getUserIdentify())) {
                    throw new IgnoreException(TechplayErrDtlEnum.LOGIN_NATIVE_CACHE_ERROR);
                }

                UserInfo userInfo = loginService.getUserInfo(user.getUserIdentify());
                if (CollectionUtils.isEmpty(userInfo.getDataAuth())) {
                    return returnValue;
                }

                if (dataPermission != null && returnValue instanceof ApiResponse) {
                    applyMaskedFields(returnValue, userInfo.getDataAuth());
                }

                // 处理queryOrderPayLog方法的返回值
                if (joinPoint.getSignature().getName().equals("queryOrderPayLog")) {
                    processOrderPayLogResult(returnValue, userInfo.getDataAuth());
                }

                return returnValue;
            } catch (Exception e) {
                log.error("Error applying data permission", e);
                throw new IgnoreException(TechplayErrDtlEnum.DATA_PARSE_ERROR);
            }
        }
        return returnValue;
    }

    private void applyMaskedFields(Object returnValue, List<String> dataAuth) throws IllegalAccessException {
        Object data = ((ApiResponse<?>) returnValue).getData();
        processObject(data, dataAuth);
    }

    private void processObject(Object obj, List<String> dataAuth) throws IllegalAccessException {
        if (obj instanceof Collection<?>) {
            for (Object item : (Collection<?>) obj) {
                processObject(item, dataAuth);
            }
        } else if (obj != null) {
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                DataPermission dataPermission = field.getAnnotation(DataPermission.class);
                if (dataPermission != null && dataAuth.contains(dataPermission.type().getCode())) {
                    applyMask(obj, field, dataPermission.type());
                }

                // 如果字段是对象类型，递归处理
                if (!field.getType().isPrimitive() && !field.getType().getName().startsWith("java.")) {
                    processObject(field.get(obj), dataAuth);
                } else if (field.getType().isArray() || Collection.class.isAssignableFrom(field.getType())) {
                    processCollection(field.get(obj), dataAuth);
                }

            }
        }
    }

    private void processCollection(Object collection, List<String> dataAuth) throws IllegalAccessException {
        if (collection instanceof Collection<?>) {
            for (Object item : (Collection<?>) collection) {
                processObject(item, dataAuth);
            }
        } else if (collection != null && collection.getClass().isArray()) {
            int length = Array.getLength(collection);
            for (int i = 0; i < length; i++) {
                processObject(Array.get(collection, i), dataAuth);
            }
        }
    }



    private void applyMask(Object returnValue, Field field, DataPermissionType type) throws IllegalAccessException {
        try {
            if (field.getType().isAssignableFrom(String.class)) {
                String originalValue = (String) field.get(returnValue);
                field.set(returnValue, applyMask(originalValue, type));
            }
            if (field.getType().isAssignableFrom(List.class)) {
                List<String> originalValue = (List<String>) field.get(returnValue);
                List<String> maskedValue = applyMaskList(originalValue, type);
                field.set(returnValue, maskedValue);
            }
        } catch (IllegalAccessException e) {
            log.error("applyMask error".concat((String) field.get(returnValue)).concat("_").concat(type.getCode()), e);
            throw e;
        }
    }

    private String applyMask(String value, DataPermissionType type) {
        if (type != null) {
            List<DictDetail> allDictById = dictDataCache.getAllDictById(1L);
            return type.maskData(value,allDictById);
        }
        return value;
    }

    private List<String> applyMaskList(List<String> value, DataPermissionType type) {
        if (type != null) {
            List<DictDetail> allDictById = dictDataCache.getAllDictById(1L);
            return type.maskDataList(value,allDictById);
        }
        return value;
    }

    /**
     * 处理queryOrderPayLog方法的返回值，对支付账号进行掩码处理
     * 如果用户没有掩码权限，payAccount需要做掩码
     * 如果payType是alipay使用前三后二的掩码规则，MASK_ALIPAY
     * 如果payType是储蓄卡前6后4，MASK_CARDNO
     *
     * @param returnValue 返回值
     * @param dataAuth 用户数据权限
     */
    private void processOrderPayLogResult(Object returnValue, List<String> dataAuth) {
        if (returnValue instanceof List) {
            List<?> list = (List<?>) returnValue;
            for (Object item : list) {
                if (item instanceof VipOrderPayLogDetailDTO) {
                    VipOrderPayLogDetailDTO dto = (VipOrderPayLogDetailDTO) item;
                    // 如果没有掩码权限，对payAccount进行掩码处理
                    if (StringUtils.isNotBlank(dto.getPayAccount())) {
                        String payType = dto.getPayType();
                        if (StringUtils.equalsAnyIgnoreCase(payType, VipPayTypeEnum.ALIPAY.getCode(), VipPayTypeEnum.WECHAT.getCode()) && dataAuth.contains(DataPermissionType.MASK_ALIPAY.getCode())) {
                            // 支付宝\微信账号使用前三后二的掩码规则
                            dto.setPayAccount(DataMaskingUtil.maskAlipay(dto.getPayAccount()));
                        } else if (StringUtils.equalsAnyIgnoreCase(payType, VipPayTypeEnum.DEBIT_CARD.getCode()) && dataAuth.contains(DataPermissionType.MASK_CARDNO.getCode())) {
                            // 储蓄卡使用前6后4的掩码规则
                            dto.setPayAccount(DataMaskingUtil.maskCardNo(dto.getPayAccount()));
                        }
                    }
                }
            }
        }
    }
}
