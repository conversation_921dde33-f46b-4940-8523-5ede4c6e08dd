/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ RepaymentProcessReq, v 0.1 2024-03-29 17:18 junjie.yan Exp $
 */
@Data
public class RefundFeeRatioProcessResp {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("建议退款金额")
    private BigDecimal suggestedRefundAmt;

    @ApiModelProperty("退款后费率")
    private BigDecimal feeRatioAfterRefund;

    @ApiModelProperty("退款中金额")
    private BigDecimal refundAmtInProcess;
}