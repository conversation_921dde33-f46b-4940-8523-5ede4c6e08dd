/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version $ GetOrderListRequest, v 0.1 2023-12-18 15:16 junjie.yan Exp $
 */

@Data
public class GetImagesRequest {

    @ApiModelProperty(value = "userNo")
    @NotBlank(message = "userNo不能为空")
    private String userNo;

    @ApiModelProperty(value = "custNo")
    private String custNo;
}