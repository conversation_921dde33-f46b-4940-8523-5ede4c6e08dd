/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.xinfei.vocmng.biz.service.SseService;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ SseConsumer, v 0.1 2024/3/7 17:25 wancheng.qu Exp $
 */
@Component
@RocketMQMessageListener(consumerGroup = "cg_vocmng_sse", topic = "tp_vocmng_sse",messageModel = MessageModel.BROADCASTING,consumeMode = ConsumeMode.CONCURRENTLY,consumeThreadNumber = 20)
@Slf4j
public class SseConsumer extends AbstractMessageListener<String> {

    @Autowired
    private SseService sseService;

    @Override
    protected void execute(String body) {
        Map<String, Object> t = new Gson().fromJson(body, new TypeToken<Map<String, Object>>(){}.getType());
        sseService.sendEventToUser(t.get("userIdentify").toString(),t);
    }
}