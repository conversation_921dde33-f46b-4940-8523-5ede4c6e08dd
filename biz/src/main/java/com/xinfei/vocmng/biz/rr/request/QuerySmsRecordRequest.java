package com.xinfei.vocmng.biz.rr.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 短信
 *
 * <AUTHOR>
 * @version $ SmsRecordRequest, v 0.1 2023/12/26 21:39 qu.lu Exp $
 */
@Data
public class QuerySmsRecordRequest {

    @ApiModelProperty("手机号码")
    @NotBlank(message = "手机号必传")
    private String mobile;

    @ApiModelProperty("业务类型")
    private String tplBizType;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty("开始时间")
    private LocalDateTime createStartTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty("结束时间")
    private LocalDateTime createEndTime;

    @ApiModelProperty(value = "每页显示条数")
    private Integer pageSize;

    @ApiModelProperty(value = "当前页")
    private Integer currentPage;
}
