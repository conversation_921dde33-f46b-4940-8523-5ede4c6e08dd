/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ StopMarketDto, v 0.1 2024/8/16 15:22 wancheng.qu Exp $
 */
@Data
public class MarketingBlackDto implements Serializable {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "主标签")
    private String mainLabelKey;

    @ApiModelProperty(value = "子标签")
    private String subLabelKey;

    @ApiModelProperty(value = "0失效，1待生效，2生效")
    private Integer status;

    @ApiModelProperty(value = "生效时间")
    private String dateStart;

    @ApiModelProperty(value = "更新时间")
    private String updatedTime;

    @ApiModelProperty(value = "失效时间")
    private String dateEnd;

    @ApiModelProperty(value = "掩码手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobileEncrypted;

    @ApiModelProperty(value = "明文手机号")
    private String mobile;

    @ApiModelProperty(value = "暂停天数")
    private Long day;

    @ApiModelProperty(value = "来源 1：新标签系统 2：老黑名单系统")
    private String source;

    @ApiModelProperty(value = "手机号关联关系")
    private String relatedRelationships;

}