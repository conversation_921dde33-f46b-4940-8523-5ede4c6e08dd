/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ VipOrderReduceDetailAdmin, v 0.1 2025-06-25 17:03 junjie.yan Exp $
 */
@Data
public class VipOrderReduceDetailAdmin {

    @ApiModelProperty("减免申请ID")
    private Long id;
    @ApiModelProperty("会员订单ID")
    private String vipOrderId;
    @ApiModelProperty("会员订单号")
    private String vipOrderNo;
    @ApiModelProperty("会员卡名称")
    private String vipCardName;
    @ApiModelProperty("签约价（元）")
    private BigDecimal contractPrice;
    @ApiModelProperty("会员订单生效开始时间")
    private LocalDateTime effectiveStartTime;
    @ApiModelProperty("会员订单生效结束时间")
    private LocalDateTime effectiveEndTime;
    @ApiModelProperty("申请人")
    private String applicant;
    @ApiModelProperty("减免金额(元)")
    private BigDecimal reduceAmount;
    @ApiModelProperty("真实售价（分）")
    private BigDecimal salePrice;
    @ApiModelProperty("方案状态(生效中:enable, 已使用:used, 已失效: disabled)")
    private String applyStatus;
    @ApiModelProperty("方案状态")
    private Integer planStatus;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("会员订单创建时间")
    private LocalDateTime vipOrderCreateTime;
    @ApiModelProperty("订单操作类型：1:首次购买，2:手动续期，3-自动续期，4-手动关闭，5-自动关闭")
    private Integer orderBuyType;
    @ApiModelProperty("会员类型 1：月卡，2:连续包月，3:季卡 4:连续包季")
    private Integer vipTerm;

}