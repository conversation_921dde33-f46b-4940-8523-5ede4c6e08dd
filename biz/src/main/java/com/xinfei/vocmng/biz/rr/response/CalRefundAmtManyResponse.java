/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ CalRefundAmtManyResponse, v 0.1 2025/07/08 15:45 pengming.liu Exp $
 */
@Data
public class CalRefundAmtManyResponse {

    @ApiModelProperty("退款金额")
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "借据号")
    private String loanNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "账单号")
    private String billNo;

    @ApiModelProperty(value = "会员卡订单id")
    private String vipCardId;
}
