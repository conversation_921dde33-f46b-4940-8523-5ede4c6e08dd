/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ VipCardRefundApply, v 0.1 2024-06-22 17:43 junjie.yan Exp $
 */
@Data
public class VipCardRefundApply {
    @ApiModelProperty("用户Id")
    @NotBlank(message = "userNo不为空")
    private String userNo;

    @ApiModelProperty("会员卡id: 黑卡，会员卡，飞向会员卡 订单id")
    @NotNull(message = "vipCardId不为空")
    private Long vipCardId;

    @ApiModelProperty("1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡 4：飞跃会员")
    @NotNull(message = "cardType不为空")
    private Integer cardType;

    @ApiModelProperty("线下还款账号")
    private String offlineAccount;

    @ApiModelProperty("1:银行卡，2:支付宝")
    private Integer offlineAccountType;

    @ApiModelProperty("退款金额：元")
    @NotNull(message = "amount不为空")
    private BigDecimal amount;

    @ApiModelProperty("退卡类型，1:退款，2:退卡&退款")
    @NotNull(message = "refundType不为空")
    private Integer refundType;

    @ApiModelProperty("退款原因")
    @NotBlank(message = "reason不为空")
    private String reason;

    @ApiModelProperty("退款方式：1:原路退回，2:线下退回 前端统一传1 具体退款逻辑由会员中心控制")
    private Integer refundChannel;

    @ApiModelProperty("线下退款，银行卡名称")
    private String offlineAccountBankName;

}