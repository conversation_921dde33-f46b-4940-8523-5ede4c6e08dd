package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 权益卡明细信息
 *
 * <AUTHOR>
 * @version $ RightCardDetail, v 0.1 2024/1/11 12:02 qu.lu Exp $
 */
@Data
public class RightCardDetailDto {
    @ApiModelProperty("")
    private Integer id;
    private Integer rightsPackId;
    @ApiModelProperty("APP")
    private String app;
    @ApiModelProperty("InnerAPP")
    private String innerApp;
    @ApiModelProperty("权益开始时间")
    private String startTime;
    @ApiModelProperty("权益结束时间")
    private String endTime;
    @ApiModelProperty("权益状态：1生效中")
    private Integer status;
    @ApiModelProperty("是否删除：0未删除")
    private Integer isDeleted;
    @ApiModelProperty("金额：元")
    private BigDecimal fee;
    @ApiModelProperty("权益明细")
    private List<RightUseDetailDto> itemInfo;
}
