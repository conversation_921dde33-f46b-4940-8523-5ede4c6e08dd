/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.mq;

import com.google.gson.Gson;
import com.xinfei.vocmng.biz.remote.MemberInterestRemoteService;
import com.xinfei.vocmng.biz.rr.request.SVipRefundResultMsg;
import com.xinfei.vocmng.util.rocketmq.AbstractMessageListener;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2025/05/09 10:34
 * 飞跃会员卡退款失败创建工单
 */
@Component
@RocketMQMessageListener(consumerGroup = "cg_vocmng_tp_supervipcore_refund_result", topic = "tp_supervipcore_refund_result")
@Slf4j
public class SVipCoreConsumer extends AbstractMessageListener<String> {

    @Autowired
    private MemberInterestRemoteService memberInterestRemoteService;

    @Override
    protected void execute(String body) {
        SVipRefundResultMsg t = new Gson().fromJson(body, SVipRefundResultMsg.class);
        if (t != null && StringUtils.isNotBlank(t.getRefundStatus()) && "refund_fail".equals(t.getRefundStatus())) {
            memberInterestRemoteService.refundVipFailed(null, t);
        }
    }
}