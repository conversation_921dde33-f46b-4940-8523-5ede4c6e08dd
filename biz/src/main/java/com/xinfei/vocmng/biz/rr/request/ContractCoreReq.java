/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @version $ ContractCoreReq, v 0.1 2024/12/16 15:27 pengming.liu Exp $
 */
@Data
public class ContractCoreReq implements Serializable {

    @ApiModelProperty("借据号")
    private String bizNo;

    @ApiModelProperty("业务类型")
    @NotNull(message = "业务类型必传 证明类文件传“loan_proof”。会员卡传“vip_card”。")
    private String bizType;

    @ApiModelProperty("签章BizNo")
    @NotNull(message = "签章BizNo必传")
    private String contractBizNo;
}