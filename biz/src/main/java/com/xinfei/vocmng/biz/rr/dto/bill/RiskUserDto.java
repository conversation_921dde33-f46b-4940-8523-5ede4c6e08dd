/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto.bill;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/7/11 上午10:43
 * RiskUserDto
 */
@Data
public class RiskUserDto {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("风险得分系数1:高风险 0:低风险")
    private Integer scoreBin;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updatedTime;
}