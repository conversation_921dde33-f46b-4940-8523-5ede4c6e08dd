/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ JobDto, v 0.1 2024-01-02 18:02 junjie.yan Exp $
 */
@Data
public class JobDto {
    @ApiModelProperty(value = "客户号")
    private String custNo;

    @ApiModelProperty(value = "用户号")
    private Long userNo;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "公司名称")
    @DataPermission(type = DataPermissionType.MASK_ADDRESS)
    private String companyName;

    @ApiModelProperty(value = "公司地址")
    @DataPermission(type = DataPermissionType.MASK_ADDRESS)
    private String companyAddr;

    @ApiModelProperty(value = "公司行政编码")
    private String companyDistrictCode;

    @ApiModelProperty(value = "公司电话")
    private String companyTelNo;

    @ApiModelProperty(value = "成员级别")
    private String jobGrade;

    @ApiModelProperty(value = "薪资")
    private String income;

    @ApiModelProperty(value = "数据是否删除(0正常，1删除)")
    private Integer isDeleted;

    @ApiModelProperty(value = "老业务表id(mongodb表id)")
    private String oldId;

}