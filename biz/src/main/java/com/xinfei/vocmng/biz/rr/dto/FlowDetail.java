/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version $ FlowDetail, v 0.1 2024-05-13 17:31 junjie.yan Exp $
 */
@Data
public class FlowDetail {

    @ApiModelProperty("交易流水号")
    @NotEmpty(message = "交易流水号不能为空")
    private String flowNo;

    @ApiModelProperty("销账金额")
    @NotNull(message = "销账金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty("流水渠道")
    private String channelCode;

}