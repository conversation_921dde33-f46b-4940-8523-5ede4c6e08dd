/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ RepaymentResponse, v 0.1 2024-03-25 13:37 junjie.yan Exp $
 */

@Data
public class RepayInitFee {

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）")
    private String orderType;

    @ApiModelProperty("初始本金")
    private BigDecimal prinAmt;

    @ApiModelProperty("初始利息")
    private BigDecimal intAmt;

    @ApiModelProperty("初始担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("担保费")
    private BigDecimal fee1Amt;

    @ApiModelProperty("反担保费")
    private BigDecimal fee2Amt;

    @ApiModelProperty("罚息")
    private BigDecimal ointAmt;

    @ApiModelProperty("贷后逾期管理费")
    private BigDecimal fee3Amt;

    @ApiModelProperty("催费")
    private BigDecimal fee6Amt;

    @ApiModelProperty("初始预期费")
    private BigDecimal lateFee;

    @ApiModelProperty("总期数")
    private Integer totalTerms;

    @ApiModelProperty("成功期数")
    private Integer actTerms;

    @ApiModelProperty("逾期期数")
    private Integer overDueTerms;

    @ApiModelProperty("最大可选期数")
    private Integer maxAbleTerms;

    @ApiModelProperty("借据状态. RP-正常, OD-逾期, FP-结清")
    private String status;

    @ApiModelProperty("是否存在生效中方案")
    private Boolean isEffectPlan;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "账单日")
    private LocalDate dateDue;

    @ApiModelProperty("是否为JD渠道限制划扣方式")
    private Boolean isJDWithhold;

    @ApiModelProperty("是否为反诈渠道限制")
    private Boolean isAntiFraud;

    @ApiModelProperty("资方")
    private String fundSource;

    @ApiModelProperty("是否为回购渠道限制")
    private Boolean isBuyBack;
}