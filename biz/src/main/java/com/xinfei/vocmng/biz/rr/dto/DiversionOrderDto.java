/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/8/28 11:52
 * DiversionOrderDto
 */
@Data
public class DiversionOrderDto {

    @ApiModelProperty("姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String name;

    @ApiModelProperty("手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobile;

    @ApiModelProperty("身份证号")
    @DataPermission(type = DataPermissionType.MASK_IDCARD)
    private String idNo;

    @ApiModelProperty("userNo")
    private String userNo;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("API产品")
    private String productName;

    @ApiModelProperty("额度")
    private String auditAmount;

    @ApiModelProperty("额度有效期")
    private String expiredTime;

    @ApiModelProperty("订单状态")
    private String status;

    @ApiModelProperty("借款期数天数")
    private String period;

    @ApiModelProperty("借款金额")
    private String amount;

    @ApiModelProperty("原因")
    private String failedReason;

    @ApiModelProperty("机构订单号")
    private String loanOrderNo;

    @ApiModelProperty("创建时间")
    private String submitTime;

    @ApiModelProperty("审核时间")
    private String auditTime;

    @ApiModelProperty("借款时间")
    private String loanApplyTime;

    @ApiModelProperty("放款时间")
    private String loanRemitTime;

}