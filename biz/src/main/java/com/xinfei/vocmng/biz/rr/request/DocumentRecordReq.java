/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinfei.vocmng.biz.model.base.PageRequestDto;
import com.xinfei.vocmng.biz.model.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ DocumentRecordReq, v 0.1 2024/5/31 07:51 wancheng.qu Exp $
 */
@Data
public class DocumentRecordReq extends PageRequestDto {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "订单号")
    @NotBlank(message = "订单号不能为空")
    private String orderNumber;

    @ApiModelProperty(value = "用户号")
    @NotBlank(message = "用户号不能为空")
    private String userNo;

    @ApiModelProperty(value = "邮箱")
    private String mail;

    @ApiModelProperty(value = "1:信飞结清证明，2:资方结清证明，3:借款凭证，4:明珠结清证明，5:居间协议")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime starTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "申请时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否立即发送，默认否")
    private boolean nowSend;


}