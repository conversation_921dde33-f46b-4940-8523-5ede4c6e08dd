/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import com.xinfei.vocmng.biz.rr.dto.BankDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentPlanResp, v 0.1 2024-04-02 10:46 junjie.yan Exp $
 */
@Data
public class RepaymentPlanResp {
    @ApiModelProperty("方案编号")
    private Long id;

    @ApiModelProperty("还款类型：1：还当期，2：提前结清")
    private Integer planType;

    @ApiModelProperty("手机号（聚合支付/线下还款使用）")
    private String mobile;

    @ApiModelProperty(value = "银行卡列表(在线抵扣使用)")
    private List<BankDto> banks;

    @ApiModelProperty("是否包含按日计息订单")
    private Boolean hasDailyType;

    @ApiModelProperty("方案状态:（0：待生效、1：生效中 、2：失效、3：成功）")
    private Integer planStatus;

    @ApiModelProperty("审批状态:（0：待审批、1：审批通过 、2：审批拒绝）")
    private Integer reviewStatus;

    @ApiModelProperty("审批原因")
    private String reviewReason;

    @ApiModelProperty("方案有效期")
    private LocalDateTime endTime;

    @ApiModelProperty("还款方式（1：app自助，2：系统代扣，3：聚合支付，4：线下还款）")
    private Integer repayMethod;

    @ApiModelProperty("一级投诉渠道")
    private String complaintChannelLv1;

    @ApiModelProperty("二级投诉渠道")
    private String complaintChannelLv2;

    @ApiModelProperty("参与抵扣借据号")
    private List<String> beReducedLoanNos;

}