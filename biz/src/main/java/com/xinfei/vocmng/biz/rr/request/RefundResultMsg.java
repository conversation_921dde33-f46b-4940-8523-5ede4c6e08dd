/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * <AUTHOR> 2024/10/10 14:00
 * @ Description:  会员卡退卡结果通知mq
 * RefundResultMsg
 */
@Data
public class RefundResultMsg {

    @ApiModelProperty(value = "会员卡id：订单id")
    private Long vipCardId;

    @ApiModelProperty(value = "订单号")
    private String orderNumber;

    @ApiModelProperty(value = "会员卡类型：1黑卡，2会员卡，3：飞享会员卡")
    private Integer vipCardType;

    @ApiModelProperty(value = "退款流水号")
    private String refundFlowNumber;

    @ApiModelProperty(value = "退款金额：分")
    private Integer amount;

    @ApiModelProperty(value = "退款状态 0:初始化退款中，1:退款成功，2:退款失败")
    private Integer status;

    @ApiModelProperty(value = "操作人：加密")
    private String operator;

    @ApiModelProperty(value = "退款原因")
    private String reason;

    @ApiModelProperty(value = "退款通道：1:原路退回，2:线下退回")
    private Integer refundChannel;

    @ApiModelProperty(value = "用户号，credit_user.id")
    private Long userNo;

    @ApiModelProperty(value = "退款账号")
    private String refundAccount;

    @ApiModelProperty(value = "退款账号类型：1，银行卡，2：支付宝")
    private Integer refundAccountType;

}