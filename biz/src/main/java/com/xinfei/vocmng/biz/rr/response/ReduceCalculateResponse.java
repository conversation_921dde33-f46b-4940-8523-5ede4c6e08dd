/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/9/9 16:29
 * ReduceCalculateResponse
 */
@Data
public class ReduceCalculateResponse {

    @ApiModelProperty("本金")
    private BigDecimal prinAmt;

    @ApiModelProperty("利息")
    private BigDecimal transInt;

    @ApiModelProperty("担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("逾期费")
    private BigDecimal overdueFee;

    @ApiModelProperty("提前结清费")
    private BigDecimal realAdvSettFee;

    @ApiModelProperty("借据号")
    private String loanNo;

    @ApiModelProperty("实际应还金额")
    private BigDecimal realAmt;

    @ApiModelProperty("是否超过权限")
    private Boolean isExceed;
}
