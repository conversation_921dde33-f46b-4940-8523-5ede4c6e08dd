package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ SmsRecordDetail, v 0.1 2023/12/27 09:33 qu.lu Exp $
 */
@Data
public class SmsRecordDto {
    @ApiModelProperty("业务类型")
    private String group;
    @ApiModelProperty("短信内容")
    private String content;
    @ApiModelProperty("模板ID")
    private String source;
    @ApiModelProperty("发送状态")
    private String deliveryStatus;
    @ApiModelProperty("发送状态")
    private String deliveryStatusDesc;
    @ApiModelProperty("状态描述")
    private String deliveryMsg;
    @ApiModelProperty("使用渠道")
    private String agent;
    @ApiModelProperty("创建时间")
    private String createdTime;
    @ApiModelProperty("模板：使用目的")
    private String aim;
    @ApiModelProperty("消息编号")
    private String batchNo;
    @ApiModelProperty("上游系统名称")
    private String appName;
}
