/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> 2024/8/12 18:08
 * 用户优惠券详情Req
 */
@Data
public class UserCouponDetailReq {

    @ApiModelProperty("userNo")
    @NotNull(message = "userNo必传")
    private Long userNo;

    @ApiModelProperty("优惠劵id列表")
    @NotEmpty(message = "优惠劵id列表不能为空")
    private List<Integer> couponIdList;
}