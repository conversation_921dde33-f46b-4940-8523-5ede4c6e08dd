/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ VipCardRefundLogReq, v 0.1 2024-06-27 22:43 junjie.yan Exp $
 */
@Data
public class VipCardRefundLogReq {

    @NotEmpty
    private List<VipCardRefundLog> vipCardRefundLogList;

    @ApiModelProperty("卡名称")
    private String cardName;

    @ApiModelProperty("退款状态 0:初始化退款中，1:退款成功，2:退款失败")
    private List<Integer> status;

}