package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.rr.response.RefundApplying;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR> 2024/6/21 10:48
 * RightCardRefundRecordDto
 */
@Data
public class VipRefundDetailDto {

    @ApiModelProperty(value = "退款账号")
    private String refundAccount;

    @ApiModelProperty(value = "bankName")
    private String bankName;

    @ApiModelProperty(value = "退款账号类型")
    private Integer refundAccountType;

    @ApiModelProperty("会员卡id")
    private String vipCardId;

    @ApiModelProperty("卡类型:1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡")
    private Integer cardType;

    @ApiModelProperty("支付订单号")
    private String orderNo;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("退款金额：元")
    private BigDecimal amount;

    @ApiModelProperty("退款方式：1:原路退回，2:线下退回")
    private Integer refundChannel;

    @ApiModelProperty("退款用户名称")
    private String name;

    @ApiModelProperty("不可退原因")
    private String reason;



}