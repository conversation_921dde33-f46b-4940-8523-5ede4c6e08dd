/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class WriteOffReversalRequest {

    @ApiModelProperty("还款编号")
    @NotBlank(message = "还款编号不能为空")
    private String repaymentNo;

    @ApiModelProperty("撤销原因")
    @NotBlank(message = "撤销原因不能为空")
    private String cancelResult;

}