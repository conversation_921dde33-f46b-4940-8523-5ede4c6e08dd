/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentProcessResp, v 0.1 2024-03-29 17:20 junjie.yan Exp $
 */
@Data
public class RepaymentProcessResp {


    @ApiModelProperty("初始费项")
    private List<RepayInitFee> repayInitFees;


    @ApiModelProperty("测算费项")
    private List<CalculateFee> calculateFees;


    @ApiModelProperty("减免额度")
    private List<ExemptionResponse> exemptions;


    @ApiModelProperty("特殊提示")
    private String toast;
}