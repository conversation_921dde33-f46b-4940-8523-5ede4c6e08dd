/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> 2024/7/16 下午2:10
 * RepayTradeDeductionCancelRequest
 */
@Data
public class RepayTradeDeductionCancelRequest {

    @ApiModelProperty("还款单号")
    @NotBlank(message = "还款单号不能为空")
    private String repaymentNo;

    @ApiModelProperty("操作人")
    @NotBlank(message = "操作人不能为空")
    private String updatedBy;
}