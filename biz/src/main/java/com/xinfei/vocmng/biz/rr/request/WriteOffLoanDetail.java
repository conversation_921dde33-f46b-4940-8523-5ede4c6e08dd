/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ WriteOffDetail, v 0.1 2024-07-16 14:52 junjie.yan Exp $
 */
@Data
public class WriteOffLoanDetail {

    @ApiModelProperty("还款类型：1：还当期，2：提前结清")
    @NotNull(message = "还款类型不能为空")
    private Integer planType;

    @ApiModelProperty("借据号")
    @NotBlank(message = "借据号不能为空")
    private String loanNo;

    @ApiModelProperty("期数")
    private List<String> terms;

    @ApiModelProperty("还款金额")
    @NotNull(message = "还款金额不能为空")
    private BigDecimal amount;

}