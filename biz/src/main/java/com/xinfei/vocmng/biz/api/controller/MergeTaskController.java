package com.xinfei.vocmng.biz.api.controller;

import com.xinfei.ssomng.facade.rr.ApiResponse;
import com.xinfei.vocmng.biz.service.MergeTaskService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version $ MergeTaskController, v 0.1 2025/4/30 15:43 shaohui.chen Exp $
 */
@RestController
@RequestMapping("/merge")
@RequiredArgsConstructor
public class MergeTaskController {

    private final MergeTaskService mergeTaskService;

    @GetMapping("/push")
    public ApiResponse<Void> push(@RequestParam Long filterId) {
        mergeTaskService.pushWebTokens(filterId, null);
        return ApiResponse.ok(null);
    }

    @GetMapping("/pushSingle")
    public ApiResponse<Void> pushSingle(@RequestParam String webToken) {
        mergeTaskService.pushSingleWebToken(webToken);
        return ApiResponse.ok(null);
    }

}
