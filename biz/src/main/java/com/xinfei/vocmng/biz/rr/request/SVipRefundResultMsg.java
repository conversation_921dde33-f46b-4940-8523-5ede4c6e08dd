/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 2025/05/09 10:34
 * @ Description:  飞跃会员卡退卡结果通知mq
 * SVipRefundResultMsg
 */
@Data
public class SVipRefundResultMsg {

    // 会员订单ID
    private Long vipOrderId;

    // 会员订单号
    private String vipOrderNo;

    // 会员类型,fei_yue:飞跃会员、fei_xiang:飞享会员
    private String vipType;

    // 退款申请流水号
    private String applyFlowNo;

    // 退款明细流水号
    private String refundFlowNo;

    // 退款号(资产交换核心)
    private String outRefundNo;

    // 退款金额：分
    private Integer amount;

    // 退款状态，refund_fail:退款失败、refund_success:退款成功
    private String refundStatus;

    // 操作人
    private String operator;

    // 退款原因
    private String refundReason;

    // 退款渠道，orignal_refund:原路退，offline_refund:线下退
    private String refundChannel;

    // 用户号
    private String userNo;

    // 退款账号
    private String refundAccount;

    // 退款账号类型：wechat:微信、alipay:支付宝、debit_card:储蓄卡
    private String refundAccountType;

    // app
    private String app;

    // 客户号
    private String custNo;

    // custNo
    private String lendOrderNo;
}