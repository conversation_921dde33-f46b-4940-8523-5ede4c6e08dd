package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 会员卡列表查询请求参数
 *
 * <AUTHOR>
 * @version $ QueryMemberCardList, v 0.1 2023/12/21 20:31 qu.lu Exp $
 */
@Data
public class QueryMemberCardListRequest {
    @ApiModelProperty("手机号码")
    private String mobile;
    @ApiModelProperty("APP名称")
    private String app;
    @ApiModelProperty(value = "userNo")
    private String userNo;

    @ApiModelProperty("姓名（新老卡）")
    private String name;
    @ApiModelProperty("卡状态：1开卡 2退卡 3失效（新老卡）")
    private Integer cardStatus;

    @ApiModelProperty(value = "orderNo支付订单号（飞享、飞跃）")
    private String orderNo;
    @ApiModelProperty("（飞享、飞跃）会员状态：0:初始未支付，1:处理中，2:支付发起，3-支付成功，4-支付失败，5-支付关闭")
    @Deprecated
    private Integer renewStatus;

    @ApiModelProperty("（飞享、飞跃）会员状态")
    private String vipStatus;
}
