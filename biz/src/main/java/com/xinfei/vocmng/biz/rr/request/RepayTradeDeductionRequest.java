/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2024/7/16 下午2:10
 * RepayTradeDeductionRequest
 */
@Data
public class RepayTradeDeductionRequest  extends PageQuery  {

    @ApiModelProperty("借据号")
    private List<String> loanNo;

    @ApiModelProperty("状态 SUCCESS成功; CANCEL撤销")
    private String status;
}