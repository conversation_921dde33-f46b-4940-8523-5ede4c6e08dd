package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询话单列表信息
 *
 * <AUTHOR>
 * @version $ CallListRequest, v 0.1 2023/12/27 21:43 qu.lu Exp $
 */
@Data
public class QueryCallListRequest {
    @ApiModelProperty("页码")
    private Integer pageNumber;
    @ApiModelProperty("每页展示大小")
    private Integer pageSize;
    @ApiModelProperty("用户编号")
    private String userNo;
    @ApiModelProperty("手机号")
    private String originMobile;
    @ApiModelProperty("系统key: 电销=telemkt;催收=hutta;客服=vocmng;")
    private String appKey;
    @ApiModelProperty("话单创建时间，开始时间 yyyy-MM-dd HH:mm:ss")
    private String startTime;
    @ApiModelProperty("话单创建时间，结束时间  yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
