/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 2025/03/20 14:52
 * OrderFilterFactorDto
 */
@Data
public class OrderFilterFactorDto {

    @ApiModelProperty("订单size")
    private Integer orderSize;

    @ApiModelProperty("资金池List")
    @DataPermission(type = DataPermissionType.MASK_MONEY)
    private List<String> capitalPoolList;

    @ApiModelProperty("innerAppList")
    private List<String> innerAppList;

}