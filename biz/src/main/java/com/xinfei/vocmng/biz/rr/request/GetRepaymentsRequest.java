/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepaymentsReq, v 0.1 2023-12-26 11:19 junjie.yan Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetRepaymentsRequest extends PageQuery {
    @ApiModelProperty(value = "借据号")
    @NotBlank(message = "借据号不能为空")
    private List<String> loanNo;

    @ApiModelProperty(value = "custNo")
    private String custNo;

    @ApiModelProperty(value = "还款状态=00：还款中、01：成功、02：完全失败、03：部分成功")
    private List<String> payStatus;

    @ApiModelProperty(value = "还款方式")
    private String payType;

}