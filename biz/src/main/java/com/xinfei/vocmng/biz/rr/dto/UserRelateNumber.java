/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ UserRelateNumber, v 0.1 2025-06-05 15:41 pengming.liu Exp $
 */
@Data
public class UserRelateNumber {
    @ApiModelProperty(value = "掩码手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String mobileEncrypted;

    @ApiModelProperty(value = "明文手机号")
    private String mobile;
}