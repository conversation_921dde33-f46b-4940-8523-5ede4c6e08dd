/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @version $ ReduceApplyRequest, v 0.1 2025-06-25 16:32 junjie.yan Exp $
 */
@Data
public class CancelVipReduceApplyRequest {

    @ApiModelProperty("方案id")
    @NotNull(message = "方案id不为空")
    private Long vipReduceId;

}