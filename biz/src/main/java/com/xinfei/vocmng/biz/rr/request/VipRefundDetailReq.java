/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/10/10 14:15
 * VipRefundDetailReq
 */
@Data
public class VipRefundDetailReq {

    @ApiModelProperty("会员卡id：黑卡，会员卡，飞享会员卡 订单id")
    @NotBlank(message = "会员卡id必传")
    private String vipCardId;

    @ApiModelProperty("卡类型:1:黑卡&黄金，2:老会员卡（御金，速通，会员卡），3:飞享会员卡，4：飞跃会员")
    @NotBlank(message = "会员卡类型必传")
    private Integer cardType;

    @ApiModelProperty("支付订单号")
    private String orderNo;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("退款金额：元")
    private BigDecimal amount;

    @ApiModelProperty("退款方式：1:原路退回，2:线下退回")
    private Integer refundChannel;

    @ApiModelProperty("退款用户名称")
    private String name;

}