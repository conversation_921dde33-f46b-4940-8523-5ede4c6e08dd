package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version $ CallRecordDto, v 0.1 2023/12/28 13:29 qu.lu Exp $
 */
@Data
public class CallRecordDto {
    @ApiModelProperty("通话编号：genesys的callId")
    private String callId;
    @ApiModelProperty("外呼类型：1-点呼，2-预测外呼，3-AI 4-IVR")
    private String outboundType;
    @ApiModelProperty("外呼类型名称")
    private String outboundTypeName;
    @ApiModelProperty("业务来源编码")
    private String bizSourceCode;
    @ApiModelProperty("业务来源名称")
    private String bizSourceName;
    @ApiModelProperty("手机号")
    private String originMobile;
    @ApiModelProperty("呼叫类型：1-呼出，2-呼入")
    private String callType;
    @ApiModelProperty("呼叫类型名称")
    private String callTypeName;
    @ApiModelProperty("拨打开始时间  yyyy-MM-dd HH:mm:ss")
    private String callStartTime;
    @ApiModelProperty("拨打结束时间 yyyy-MM-dd HH:mm:ss")
    private String callEndTime;
    @ApiModelProperty("外呼结果,1-接通，2-未接通，3-溢出，4-未拨打")
    private String callResult;
    @ApiModelProperty("外呼结果名称")
    private String callResultName;
    @ApiModelProperty("技能组名称")
    private String skillGroupName;
    @ApiModelProperty("坐席编号")
    private String agentNo;
    @ApiModelProperty("坐席名称")
    private String agentName;
}
