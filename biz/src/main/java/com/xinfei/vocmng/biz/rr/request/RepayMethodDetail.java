/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 2024/8/2 11:47
 */
@Data
public class RepayMethodDetail {

    @ApiModelProperty("借据号")
    @NotNull
    private String loanNo;

    @ApiModelProperty(value = "实际还款金额")
    @NotNull
    private BigDecimal repaymentAmount;

    @ApiModelProperty("期数")
    private List<String> terms;
}