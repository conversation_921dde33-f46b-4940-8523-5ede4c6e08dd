/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会员卡支付明细请求
 *
 * <AUTHOR>
 * @version $ VipOrderPayLogRequest, v 0.1 2025/5/09 15:35 shaohui.chen Exp $
 */
@Data
@ApiModel(description = "会员卡支付明细请求")
public class VipOrderPayLogRequest {

    @ApiModelProperty(value = "会员卡订单ID", required = true)
    @NotNull(message = "会员卡订单ID不能为空")
    private Long cardId;
}
