/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * <AUTHOR> 2024/9/5 18:02
 * ReduceCalculateReq
 */
@Data
public class ReduceCalculateReq {

    @ApiModelProperty("本金")
    private BigDecimal prinAmt;

    @ApiModelProperty("利息")
    private BigDecimal transInt;

    @ApiModelProperty("担保费")
    private BigDecimal guaranteeFee;

    @ApiModelProperty("逾期费")
    private BigDecimal overdueFee;

    @ApiModelProperty("提前结清费")
    private BigDecimal realAdvSettFee;

    @ApiModelProperty("实际应还金额")
    private BigDecimal realAmt;

    @ApiModelProperty("原来应还金额")
    private BigDecimal oldRealAmt;

    @ApiModelProperty("实际应还金额总额")
    private BigDecimal realAmtSum;

    @ApiModelProperty("借据号")
    private String loanNo;
}