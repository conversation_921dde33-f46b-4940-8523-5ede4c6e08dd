/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.aop;

/**
 * <AUTHOR>
 * @version $ OperateLogAspect, v 0.1 2023/12/29 11:42 wancheng.qu Exp $
 */

import com.alibaba.fastjson.JSON;
import com.xinfei.vocmng.biz.model.annotation.JsonIgnoreParameter;
import com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation;
import com.xinfei.vocmng.biz.service.AccountService;
import com.xinfei.vocmng.biz.util.UserContextHolder;
import com.xinfei.vocmng.dal.po.OperateLog;
import com.xinfei.vocmng.itl.client.feign.service.CisFacadeClientService;
import com.xinfei.xfframework.common.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Aspect
@Component
@Order(3)
public class OperateLogAspect {

    private final AccountService accountService;
    private final CisFacadeClientService cisFacadeClient;

    @Autowired
    public OperateLogAspect(AccountService accountService,CisFacadeClientService cisFacadeClient) {
        this.accountService = accountService;
        this.cisFacadeClient = cisFacadeClient;
    }

    @Pointcut("@annotation(com.xinfei.vocmng.biz.model.annotation.OperateLogAnnotation)")
    public void logPointCut() {

    }

    @Around("logPointCut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        OperateLog op = new OperateLog();
        try {
            HttpServletRequest request = getHttpServletRequest();
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            String className = point.getTarget().getClass().getName();
            String methodName = signature.getName();
            String fullMethodName = className + "." + methodName + "()";
            OperateLogAnnotation syslog = method.getAnnotation(OperateLogAnnotation.class);
            op.setUrl(request == null ? fullMethodName : request.getRequestURL().toString().concat("===>").concat(fullMethodName));
            op.setRequestParam(cisFacadeClient.getEncodeMobileLocal(JSON.toJSONString(filterRequestParameter(point))));
            op.setDescription(syslog.description());
            op.setType(syslog.type().getCode());
            op.setUserIdentify(UserContextHolder.getUserIdentify() == null ? getRequestIp() : UserContextHolder.getUserIdentify());
            op.setCreatedTime(LocalDateTime.now());
            op.setUpdatedTime(LocalDateTime.now());
        } catch (Exception e) {
            log.error("OperateLogAspect error", e);
        }
        long beginTime = System.currentTimeMillis();
        log.info("请求操作日志：" + JsonUtil.toJson(op));
        Object result = point.proceed();
        long time = System.currentTimeMillis() - beginTime;
        op.setResponse(cisFacadeClient.getEncodeMobileLocal(JSON.toJSONString(result)));
        op.setDescription(op.getDescription() + "==>请求耗时" + time);
        accountService.addOperateLog(op);
        return result;
    }


    private List<Object> filterRequestParameter(ProceedingJoinPoint point) {
        Object[] args = point.getArgs();
        Stream<?> stream = ArrayUtils.isEmpty(args) ? Stream.empty() : Arrays.stream(args.clone());
        return stream.filter(arg ->
                (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse))
                        && !arg.getClass().isAnnotationPresent(JsonIgnoreParameter.class)
        ).map(arg -> {
            if (arg instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) arg;
                Map<String, Object> fileDetails = Collections.emptyMap();
                if (Objects.nonNull(file)) {
                    fileDetails = Collections.singletonMap(
                            "fileName", file.getOriginalFilename()
                    );
                }
                return fileDetails;
            } else {
                return arg;
            }
        }).collect(Collectors.toList());
    }


    public HttpServletRequest getHttpServletRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        return ((ServletRequestAttributes) requestAttributes).getRequest();
    }

    private String getRequestIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            return request.getRemoteAddr();
        }
        return "";
    }
}
