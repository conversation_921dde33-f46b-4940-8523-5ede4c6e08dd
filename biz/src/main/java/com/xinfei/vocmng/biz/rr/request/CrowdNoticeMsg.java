package com.xinfei.vocmng.biz.rr.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 人群通知消息
 *
 * <AUTHOR>
 * @version $ CrowdNoticeMsg, v 0.1 2025/7/16 10:00 shaohui.chen Exp $
 */
@Data
public class CrowdNoticeMsg {

    @ApiModelProperty(value = "消息类型：1 上线，2 下线，3 删除，4 刷新")
    private Integer messageType;

    @ApiModelProperty(value = "人群 id")
    private Long crowdId;

    @ApiModelProperty(value = "oss 文件地址目录")
    private String ossPath;

    @ApiModelProperty(value = "oss 文件名称")
    private List<String> ossFiles;

    @ApiModelProperty(value = "人群大小")
    private Long crowdSize;

    @ApiModelProperty(value = "人群运行版本")
    private String runVersion;

    @ApiModelProperty(value = "人群规则版本")
    private String ruleVersion;

    @ApiModelProperty(value = "有效期开始时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectStartTime;

    @ApiModelProperty(value = "有效期结束时间，格式：yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectEndTime;

    @ApiModelProperty(value = "是否永久有效")
    private Boolean isPermanent;

    @ApiModelProperty(value = "判定时的 uniqukey 的拼接顺序，分割符为：&!&!&")
    private String uniqueKeyRule;
}
