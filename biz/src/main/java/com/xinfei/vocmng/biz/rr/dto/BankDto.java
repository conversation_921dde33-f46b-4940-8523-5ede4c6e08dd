/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import com.xinfei.vocmng.biz.model.annotation.DataPermission;
import com.xinfei.vocmng.biz.model.enums.DataPermissionType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ BankDto, v 0.1 2023-12-25 16:06 junjie.yan Exp $
 */
@Data
public class BankDto {

    @ApiModelProperty(value = "绑卡id")
    private Long cardId;

    @ApiModelProperty(value = "银行名称")
    private String bankName;

    @ApiModelProperty(value = "1-默认卡 2-绑定 3-解绑")
    private Integer status;

    @ApiModelProperty(value = "银行卡号")
    @DataPermission(type = DataPermissionType.MASK_CARDNO)
    private String cardNo;

    @ApiModelProperty(value = "银行手机号")
    @DataPermission(type = DataPermissionType.MASK_TELE)
    private String reservedMobileNo;

    @ApiModelProperty(value = "客户姓名")
    @DataPermission(type = DataPermissionType.MASK_NAME)
    private String custName;

    @ApiModelProperty(value = "签约状态，00-未签约，01-已签约")
    private String signed;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "绑定源")
    private String sourceChannel;

    @ApiModelProperty(value = "绑卡时间")
    private String bindTime;

    @ApiModelProperty(value = "绑卡时间")
    private LocalDateTime bindCardTime;
}