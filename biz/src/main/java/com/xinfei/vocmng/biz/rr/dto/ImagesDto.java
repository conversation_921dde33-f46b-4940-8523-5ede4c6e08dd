/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version $ ImagesDto, v 0.1 2024-01-02 20:01 junjie.yan Exp $
 */
@Data
public class ImagesDto {
    @ApiModelProperty(value = "身份证前面照（决策通过的）")
    private String idCardFront;

    @ApiModelProperty(value = "身份证背面照（决策通过的）")
    private String idCardBack;

    @ApiModelProperty(value = "身份证头像照（决策通过的）")
    private String idCardHead;

    @ApiModelProperty(value = "最佳人脸照（由传参决定）")
    private String faceBest;

    @ApiModelProperty(value = "校验时间")
    private LocalDateTime faceVerifyTime;

}