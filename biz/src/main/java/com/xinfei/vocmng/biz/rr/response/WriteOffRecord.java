/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ writeOffRecordRequest, v 0.1 2024-05-15 17:47 junjie.yan Exp $
 */
@Data
public class WriteOffRecord {

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("还款单号,撤销时用")
    private String repaymentNo;

    @ApiModelProperty("账务入账金额")
    private BigDecimal transTotalAmt;

    @ApiModelProperty("总销账金额")
    private BigDecimal transAmt;

    @ApiModelProperty("该流水销账金额")
    private BigDecimal flowTransAmt;

    @ApiModelProperty("订单号")
    private String loanNo;

    @ApiModelProperty("账单号")
    private List<String> planNo;

    @ApiModelProperty("状态 01成功,02撤销")
    private String status;

    @ApiModelProperty("操作人")
    private String createdBy;
}