/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.biz.rr.request;

import com.xinfei.vocmng.biz.rr.dto.FlowDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version $ RepayPlanRequest, v 0.1 2024-03-28 16:47 junjie.yan Exp $
 */
@Data
public class WriteOffRequest {

    @ApiModelProperty("销账明细")
    @NotEmpty(message = "销账明细为空")
    @Valid
    private List<WriteOffLoanDetail> writeOffDetails;

    @ApiModelProperty("流水列表")
    private List<FlowDetail> flowDetails;
}