package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-23 04:26:36
 */
@Data
@Accessors(chain = true)
@TableName("asr_info")
public class AsrInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * "自增id"
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * "source_id:会话id,文本所在会话的id(udesk传入的call_id)"
     */
    @TableField("call_id")
    private String callId;

    /**
     * "文本id(同一个句子多个请求id一样)"
     */
    @TableField("info_id")
    private String infoId;

    /**
     * "文本开始时间,毫秒时间戳"
     */
    @TableField("start_timestamp")
    private Long startTimestamp;

    /**
     * "文本结束时间,毫秒时间戳"
     */
    @TableField("end_timestamp")
    private Long endTimestamp;

    /**
     * "文本内容"
     */
    @TableField("text")
    private String text;

    /**
     * "文本状态,WHOLE 整个句子 / PREFIX 句子前缀 / REWRITE 句子修正"
     */
    @TableField("state")
    private String state;

    /**
     * "该段文本的说话人,CUSTOMER 客户 / STAFF员工"
     */
    @TableField("speaker")
    private String speaker;

    @TableField("created_time")
    private LocalDateTime createdTime;

    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
