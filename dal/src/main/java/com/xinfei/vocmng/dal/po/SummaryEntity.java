/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ SummaryEntity, v 0.1 2024-07-11 16:15 junjie.yan Exp $
 */
@Data
public class SummaryEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ("用户名加密")
     */
    @TableField("user_name_cipher")
    private String userNameCipher;

    /**
     * ("手机号加密")
     */
    @TableField("mobile_cipher")
    private String mobileCipher;

    /**
     * ("手机密文")
     */
    @TableField("mobile_protyle")
    private String mobileProtyle;

    /**
     * ("手机密文加密")
     */
    @TableField("mobile_protyle_cipher")
    private String mobileProtyleCipher;

    /**
     * ("身份证号加密")
     */
    @TableField("id_card_number_cipher")
    private String idCardNumberCipher;

    /**
     * ("身份证密文")
     */
    @TableField("id_card_protyle")
    private String idCardProtyle;

    /**
     * ("身份证密文加密")
     */
    @TableField("id_card_protyle_cipher")
    private String idCardProtyleCipher;

    /**
     * ("用户来源")
     */
    @TableField("user_source")
    private String userSource;

    /**
     * ("产品类型")
     */
    @TableField("product_type")
    private Integer productType;

    /**
     * ("问题描述")
     */
    @TableField("question_content")
    private String questionContent;

    /**
     * ("问题处理结果 1:已反馈，跟进中 2：已处理，已回复 3:不用跟进 4: 不用跟进")
     */
    @TableField("question_result")
    private Integer questionResult;

    /**
     * ("创建人加密")
     */
    @TableField("create_person_cipher")
    private String createPersonCipher;

    /**
     * ("创建时间")
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * ("更新时间")
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * ("订单号")
     */
    @TableField("order_number")
    private String orderNumber;

    /**
     * ("app类型")
     */
    @TableField("app_type")
    private Integer appType;

    /**
     * ("最后处理人加密")
     */
    @TableField("last_person_cipher")
    private String lastPersonCipher;

    /**
     * ("最后处理时间")
     */
    @TableField("last_time")
    private LocalDateTime lastTime;

    /**
     * ("客诉问题： 0 咨询 1 投诉升级")
     */
    @TableField("question_type")
    private Integer questionType;

    /**
     * ("用户类别 1普通用户 2投诉用户 3关怀用户")
     */
    @TableField("user_category")
    private Integer userCategory;

    /**
     * ("是否黑名单1是")
     */
    @TableField("is_black")
    private Integer isBlack;

    /**
     * ("是否发送工单：是 否")
     */
    @TableField("is_work")
    private String isWork;

    /**
     * ("资金池")
     */
    @TableField("fund_source")
    private String fundSource;

    /**
     * ("市场推广渠道")
     */
    @TableField("utm_source")
    private String utmSource;

    /**
     * ("内嵌应用")
     */
    @TableField("inner_app")
    private String innerApp;

    /**
     * ("app")
     */
    @TableField("app")
    private String app;

    /**
     * ("credit_user.id")
     */
    @TableField("user_id")
    private Long userId;

    /**
     * ("用户来电手机号-密文")
     */
    @TableField("in_mobile_protyle")
    private String inMobileProtyle;

    /**
     * ("用户来电手机号-密文加密")
     */
    @TableField("in_mobile_protyle_cipher")
    private String inMobileProtyleCipher;

    /**
     * ("用户来电手机号-归属地")
     */
    @TableField("in_mobile_cis")
    private String inMobileCis;

    /**
     * ("用户回电手机号-密文")
     */
    @TableField("out_mobile_protyle")
    private String outMobileProtyle;

    /**
     * ("用户回电手机号-密文加密")
     */
    @TableField("out_mobile_protyle_cipher")
    private String outMobileProtyleCipher;

    /**
     * ("用户回电手机号-归属地")
     */
    @TableField("out_mobile_cis")
    private String outMobileCis;

    /**
     * ("是否为黑产 1:是 0:不是")
     */
    @TableField("is_dark_industry")
    private Integer isDarkIndustry;

    /**
     * ("疑似黑产打标原因")
     */
    @TableField("dark_industry_reason")
    private String darkIndustryReason;

    /**
     * ("一级问题分类")
     */
    @TableField("question_lv1")
    private String questionLv1;

    /**
     * ("二级问题分类")
     */
    @TableField("question_lv2")
    private String questionLv2;

}