package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 流水与借据映射关系
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14 03:55:47
 */
@Data
@TableName("flow_loan_mapping")
// "流水与借据映射关系"
public class FlowLoanMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 流水号
     */
    @TableField("flow_no")
    private String flowNo;

    /**
     * 借据号
     */
    @TableField("loan_no")
    private String loanNo;

    /**
     * 账单号
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 期数
     */
    @TableField("term")
    private Integer term;

    /**
     * 类型（1：销账，2：溢缴款退款）
     */
    @TableField("type")
    private Integer type;

    /**
     * 是否逻辑删除（0：否，1：是）
     */
    @TableField("is_del")
    private Integer isDel;
}
