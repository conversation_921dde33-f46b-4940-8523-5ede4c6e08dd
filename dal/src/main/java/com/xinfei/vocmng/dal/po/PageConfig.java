package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 页面配置信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("page_config")
public class PageConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 用户身份 */
    @TableField("user_identify")
    private String userIdentify;

    /** 页面标识 */
    @TableField("page")
    private String page;

    /** 配置信息 */
    @TableField("config")
    private String config;

    /** 类型：1全局配置 2用户配置 */
    @TableField("type")
    private Integer type;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
