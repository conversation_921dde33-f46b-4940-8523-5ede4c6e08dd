package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 员工表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("employee")
public class Employee implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 员工编号 */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 身份标识 */
    @TableField("user_identify")
    private String userIdentify;

    /** 员工姓名 */
    @TableField("name")
    private String name;

    /** 手机号码 */
    @TableField("mobile")
    private String mobile;

    /** 部门ID */
    @TableField("department_id")
    private Long departmentId;

    @TableField("sso_user_id")
    private Long ssoUserId;

    /** 员工账号状态：0正常 1关闭 */
    @TableField("state")
    private Integer state;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


    @TableField("mobile_encrypted")
    private String mobileEncrypted;

    /** 是否实时质检辅助(0:否,1:是) */
    @TableField("real_time_assistance")
    private Integer realTimeAssistance;
}
