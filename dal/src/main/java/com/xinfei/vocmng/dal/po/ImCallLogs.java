package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("im_call_logs")
public class ImCallLogs {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 对应接口返回值里的 id */
    @TableField("call_log_id")
    private Long callLogId;

    /** 业务记录id */
    @TableField("note_id")
    private Long noteId;

    /** 通话开始时间 */
    @TableField("call_start_at")
    private LocalDateTime callStartAt;

    /** 通话类型("呼入","呼出") */
    @TableField("call_type")
    private String callType;

    /** 主叫号码加密 */
    @TableField("call_number_cipher")
    private String callNumberCipher;

    /** 号码归属地 */
    @TableField("mobile_area")
    private String mobileArea;

    /** 中继号 */
    @TableField("trunk_number")
    private String trunkNumber;

    /** 客户姓名加密 */
    @TableField("user_name_cipher")
    private String userNameCipher;

    /** 客户id */
    @TableField("user_id")
    private Long userId;

    /** 角色名称 */
    @TableField("call_source")
    private String callSource;

    /** 通话来源("客服: xx","队列: xx","负责人: xx","未选择队列") */
    @TableField("queue_type")
    private String queueType;

    /** 排队状态("排队成功","放弃排队","排队超时","无客服在线") */
    @TableField("queue_time")
    private Integer queueTime;

    /** 客服ID */
    @TableField("agent_id")
    private Long agentId;

    /** 客服姓名加密 */
    @TableField("agent_nick_name_cipher")
    private String agentNickNameCipher;

    /** 客服姓名 */
    @TableField("agent_nick_name")
    private String agentNickName;

    /** 客服邮箱 */
    @TableField("agent_email")
    private String agentEmail;

    /** 设备状态("ip座机","手机") */
    @TableField("device_info")
    private String deviceInfo;

    /** 通话结果("客户未接","客户接听","客服未接","客服拒接","客服接听","未选择队列") */
    @TableField("call_result")
    private String callResult;

    /** 振铃时间 */
    @TableField("ring_time")
    private Integer ringTime;

    /** 保持时长 */
    @TableField("hold_duration")
    private Integer holdDuration;

    /** 挂断方("客户","客服") */
    @TableField("drop_side")
    private String dropSide;

    /** 通话时间 */
    @TableField("call_time")
    private Integer callTime;

    /** 留言 */
    @TableField("leave_message")
    private String leaveMessage;

    /** 客户所属公司id */
    @TableField("organization_id")
    private String organizationId;

    /** 满意度评价("满意","不满意","未评价"或"无需评价") */
    @TableField("satisfaction")
    private String satisfaction;

    /** 满意度评价（新的满意度评价字段，支持智能路由的自定义满意度评价，且兼容快速路由的默认满意度评价） */
    @TableField("survey")
    private String survey;

    /** ivr时长 */
    @TableField("ivr_time")
    private Long ivrTime;

    /** 溢出队列 */
    @TableField("queue_overflow")
    private String queueOverflow;

    /** 自动外呼任务名称 */
    @TableField("ad_task_name")
    private String adTaskName;

    /** ivr变量 */
    @TableField("ivr_variables")
    private String ivrVariables;

    /** 外呼失败原因（开通后显示） */
    @TableField("defeat_cause")
    private String defeatCause;

    /** 外线号码 */
    @TableField("outline_phone_number")
    private String outlinePhoneNumber;

    /** 顺振次数 */
    @TableField("multi_ring_count")
    private Long multiRingCount;

    /** 工单编号 */
    @TableField("tickets")
    private String tickets;

    /** 后续通话 */
    @TableField("has_subsequent_call")
    private String hasSubsequentCall;

    /** 通话唯一标识 */
    @TableField("call_id")
    private String callId;

    @TableField("dtmf")
    private String dtmf;

    @TableField("callout_task_name")
    private String calloutTaskName;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 修改时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 通话咨询的持续时长 */
    @TableField("consult_duration")
    private Integer consultDuration;

    /** 录音文件地址 */
    @TableField("record_url")
    private String recordUrl;

    @TableField("callout_task_id")
    private String calloutTaskId;
}