package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("im_sessions")
public class ImSessions {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** id（同 im_sub_session_id） */
    @TableField("sub_session_id")
    private Long subSessionId;

    /** 会话 id */
    @TableField("session_id")
    private Long sessionId;

    /** 客服系统侧机器人会话 id */
    @TableField("robot_session_id")
    private Long robotSessionId;

    /** 业务记录 id */
    @TableField("note_id")
    private Long noteId;

    /** 客户 id */
    @TableField("customer_id")
    private Long customerId;

    /** 客户姓名加密 */
    @TableField("customer_name_cipher")
    private String customerNameCipher;

    /** 客服 id */
    @TableField("agent_id")
    private Long agentId;

    /** 客服姓名加密 */
    @TableField("agent_nick_name_cipher")
    private String agentNickNameCipher;

    /** 客服姓名 */
    @TableField("agent_nick_name")
    private String agentNickName;

    /** 响应时间，单位秒 */
    @TableField("resp_seconds")
    private Integer respSeconds;

    /** 排队时间，单位秒 */
    @TableField("queue_seconds")
    private String queueSeconds;

    /** 持续时间 */
    @TableField("sustain_seconds")
    private Integer sustainSeconds;

    /** 满意度调查结果 id */
    @TableField("survey_vote_id")
    private Integer surveyVoteId;

    /** 满意度-是否已解决，取值："0"、"1"，说明：解决、未解决 */
    @TableField("resolved_state")
    private String resolvedState;

    /** 满意度*/
    @TableField("option_name")
    private String optionName;

    /** 渠道，取值：web、微信、微博、android、ios、api */
    @TableField("platform")
    private String platform;

    /** 创建时间 */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /** 关闭时间 */
    @TableField("closed_at")
    private LocalDateTime closedAt;

    /** 对话结束方式，取值："agent_close"、"redirect_close"、"sys_close"、"customer_close"，说明：客服关闭、转接关闭、系统关闭、客户关闭 */
    @TableField("close_method")
    private String closeMethod;

    /** 排队队列 */
    @TableField("belong_queue")
    private String belongQueue;

    /** 客服消息数 */
    @TableField("agent_msg_num")
    private Integer agentMsgNum;

    /** 客户消息数 */
    @TableField("customer_msg_num")
    private Integer customerMsgNum;

    /** 来源 */
    @TableField("source")
    private String source;

    /** 排队开始时间 */
    @TableField("queue_start_time")
    private LocalDateTime queueStartTime;

    /** 当日对话次数 */
    @TableField("conversations_num_today")
    private Integer conversationsNumToday;

    /** 搜索关键词 */
    @TableField("search_keyword")
    private String searchKeyword;

    /** 自定义渠道信息 */
    @TableField("custom_channel")
    private String customChannel;

    /** 客服邀评次数 */
    @TableField("agent_invite_vote_count")
    private Integer agentInviteVoteCount;

    /** 最后消息发送方，取值：customer、agent、blank */
    @TableField("last_response")
    private String lastResponse;

    /** 报警次数 */
    @TableField("alert_num")
    private Integer alertNum;

    /** 报警项 */
    @TableField("alert_desc")
    private String alertDesc;

    /** 工单数量 */
    @TableField("ticket_num")
    private Integer ticketNum;

    /** 工单编号，已逗号","分割 */
    @TableField("ticket_nos")
    private String ticketNos;

    /** 来源插件 ID */
    @TableField("im_web_plugin_id")
    private Integer imWebPluginId;

    /** 对话发起方，取值："customer、agent、sys"，说明："客户、客服、系统" */
    @TableField("sender")
    private String sender;

    /** 访客邀请，取值："agent、sys、blank"，说明："客服、自动、无" */
    @TableField("active_guest")
    private String activeGuest;

    /** 公司id */
    @TableField("organization_id")
    private Integer organizationId;

    /** 导航菜单名称 */
    @TableField("menu_names")
    private String menuNames;

    @TableField("resolved_state_title")
    private String resolvedStateTitle;

    @TableField("resolved_state_name")
    private String resolvedStateName;

    @TableField("resolved_state_value")
    private String resolvedStateValue;

    @TableField("transfer_to_agent")
    private String transferToAgent;

    /** 机器人id */
    @TableField("robot_id")
    private Integer robotId;

    /** 机器人名字 */
    @TableField("robot_name")
    private String robotName;

    /** 机器人会话id */
    @TableField("robot_side_session_id")
    private String robotSideSessionId;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 修改时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 客户自定义字段，详见上文 */
    @TableField("customer_custom_fields")
    private String customerCustomFields;

    /** 浏览器访问信息，详见上文 */
    @TableField("web_info")
    private String webInfo;

    /** 微信访问信息，详见上文 */
    @TableField("weixin_info")
    private String weixinInfo;

    /** 微博访问信息，详见上文 */
    @TableField("weibo_info")
    private String weiboInfo;

    /** API 访问信息 */
    @TableField("api_info")
    private String apiInfo;

    /** iOS SDK 访问信息 */
    @TableField("ios_info")
    private String iosInfo;

    /** Android SDK 访问信息 */
    @TableField("android_info")
    private String androidInfo;

    /** 来源 URL */
    @TableField("source_url")
    private String sourceUrl;

    /** 此对话记录对应的工单 */
    @TableField("ticket_ids")
    private String ticketIds;
}
