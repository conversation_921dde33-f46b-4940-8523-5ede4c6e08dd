package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-21 11:13:38
 */

@Accessors(chain = true)
@TableName("refund_request")
@Data
public class RefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 借据号
     */
    @TableField("loan_no")
    private String loanNo;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 账单号
     */
    @TableField("bill_no")
    private String billNo;

    /**
     * 期数
     */
    @TableField("term")
    private Integer term;

    /**
     * 申请单状态，0：无需审核，1：待审核，2：审核通过，3：审核拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * custNo
     */
    @TableField("cust_no")
    private String custNo;

    /**
     * userNo
     */
    @TableField("user_no")
    private String userNo;

    /**
     * 线下退款方式（1：支付宝，2：银行卡）
     */
    @TableField("offline_refund_method")
    private Integer offlineRefundMethod;

    /**
     * 线下退款账户
     */
    @TableField("offline_refund_account")
    private String offlineRefundAccount;

    /**
     * 线下退款银行
     */
    @TableField("offline_refund_bank")
    private String offlineRefundBank;

    /**
     * 线下退款客户名
     */
    @TableField("offline_refund_user_name")
    private String offlineRefundUserName;

    /**
     * 退款金额(分)
     */
    @TableField("refund_amount")
    private Long refundAmount;

    /**
     * 退款方式（1：线上原路原退，2：线下退款）
     */
    @TableField("refund_type")
    private Integer refundType;

    /**
     * 还款时间，1：立即(2小时)，2：50天
     */
    @TableField("execute_type")
    private Integer executeType;

    /**
     * 自定义天数
     */
    @TableField("execute_day")
    private Integer executeDay;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    private LocalDateTime refundTime;

    /**
     * 创建时间
     */
    @TableField("crate_time")
    private LocalDateTime crateTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 是否逻辑删（0：否，1：是）
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 退款类型（1：订单，2：账单，3：溢缴款）
     */
    @TableField("request_type")
    private Integer requestType;

    /**
     * 订单类型，PROFIT:营收订单（权益产品订单），MAIN:现金订单（信贷产品订单）
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 审核人
     */
    @TableField("reviewer")
    private String reviewer;

    /**
     * 退款单号
     */
    @TableField("refund_order_id")
    private String refundOrderId;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款原因
     */
    @TableField("fee_subject_enum")
    private String feeSubjectEnum;

    /**
     * 审批原因
     */
    @TableField("review_reason")
    private String reviewReason;

    /**
     * 退款单号
     */
    @TableField("repayment_nos")
    private String repaymentNos;

    /**
     * 实际费率
     */
    @TableField("real_ratio")
    private BigDecimal realRatio;
}
