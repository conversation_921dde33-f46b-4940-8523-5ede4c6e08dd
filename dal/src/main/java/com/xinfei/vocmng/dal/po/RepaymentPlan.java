package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 还款方案
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27 10:27:00
 */

@Accessors(chain = true)
@TableName("repayment_plan")
@Data
public class RepaymentPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 还款方案编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * custNo
     */
    @TableField("cust_no")
    private String custNo;

    /**
     * userNo
     */
    @TableField("user_no")
    private String userNo;

    /**
     * 方案类型：1：还当期，2：提前结清
     */
    @TableField("plan_type")
    private Integer planType;

    /**
     * 方案状态:（0：待生效、1：生效中 、2：失效、3：成功）
     */
    @TableField("plan_status")
    private Integer planStatus;

    /**
     * 审批状态:（0：审批中、1：审批通过 2：审批驳回）
     */
    @TableField("review_status")
    private Integer reviewStatus;

    /**
     * 审批理由
     */
    @TableField("review_reason")
    private String reviewReason;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 更新人
     */
    @TableField("updater")
    private String updater;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /**
     * 方案结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 是否删除（1：是，0：否）
     */
    @TableField("is_del")
    private Integer isDel;

    /**
     * 手机号（聚合支付、线下还款使用）
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 银行卡绑卡id，系统代扣必传
     */
    @TableField(value = "bank_card_id")
    private String bankCardId;


    /**
     * 收银台渲染唯一key
     */
    @TableField("render_key")
    private String renderKey;

    /**
     * 一级投诉渠道
     */
    @TableField("complaint_channel_lv1")
    private String complaintChannelLv1;

    /**
     * 二级投诉渠道
     */
    @TableField("complaint_channel_lv2")
    private String complaintChannelLv2;

    /**
     * 参与抵扣的借据号
     */
    @TableField("be_reduced_loan_nos")
    private String beReducedLoanNos;
}
