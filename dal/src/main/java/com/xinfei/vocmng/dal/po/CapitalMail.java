/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

/**
 *
 * <AUTHOR>
 * @version $ CapitalMail, v 0.1 2024/5/31 05:02 wancheng.qu Exp $
 */

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("capital_mail")
public class CapitalMail {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 资方 */
    @TableField("capital")
    private String capital;

    /** 收件箱 */
    @TableField("inbox")
    private String inbox;

    /** 抄送箱 */
    @TableField("cbox")
    private String cbox;

    /** 创建人操作人身份标识 */
    @TableField("create_user")
    private String createUser;

    /** 修改人操作人身份标识 */
    @TableField("update_user")
    private String updateUser;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField(value = "updated_time", update = "CURRENT_TIMESTAMP")
    private LocalDateTime updatedTime;
}
