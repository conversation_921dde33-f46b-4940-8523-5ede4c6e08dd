/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ DictDetail, v 0.1 2024/4/9 16:47 wancheng.qu Exp $
 */

@Data
@TableName("dict_detail")
public class DictDetail implements Serializable {


    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 字典配置表ID */
    @TableField("dict_config_id")
    private Long dictConfigId;

    /** 字典名称 */
    @TableField("dict_key")
    private String dictKey;

    /** 字典值 */
    @TableField("dict_value")
    private String dictValue;

    /** 操作人身份标识 */
    @TableField("user_identify")
    private String userIdentify;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

}
