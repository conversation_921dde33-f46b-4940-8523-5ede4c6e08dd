package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("resource")
public class Resource implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 资源名称 */
    @TableField("name")
    private String name;

    /** 父资源ID */
    @TableField("parent_id")
    private Long parentId;

    /** 资源排序 */
    @TableField("order_index")
    private Integer orderIndex;

    /** 权限标识 */
    @TableField("permission_identify")
    private String permissionIdentify;

    /** 资源编码 */
    @TableField("code")
    private String code;

    /** 资源路径 */
    @TableField("path")
    private String path;

    /** 资源类型：1菜单 2页面 3按钮 */
    @TableField("type")
    private Integer type;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;
}
