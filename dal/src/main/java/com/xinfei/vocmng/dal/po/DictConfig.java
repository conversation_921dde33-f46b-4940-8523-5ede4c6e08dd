/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ DictConfig, v 0.1 2024/4/9 16:44 wancheng.qu Exp $
 */

@Data
@TableName("dict_config")
public class DictConfig implements Serializable {


    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 字典场景名称 */
    @TableField("name")
    private String name;

    /** 字典场景描述 */
    @TableField("description")
    private String description;

    /** 创建人操作人身份标识 */
    @TableField("create_user")
    private String createUser;

    /** '修改人操作人身份标识' */
    @TableField("update_user")
    private String updateUser;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    public DictConfig() {
    }

    public DictConfig(Long id) {
        this.id = id;
    }
}

