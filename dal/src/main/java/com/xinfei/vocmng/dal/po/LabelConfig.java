package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12 02:21:23
 */
@Data
@TableName("label_config")
public class LabelConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 标签类型ID */
    @TableField("category_id")
    private Long categoryId;

    /** 标签名称 */
    @TableField("name")
    private String name;

    /** 标签颜色 */
    @TableField("color")
    private String color;

    /** 标签内容方案 */
    @TableField("solution")
    private String solution;

    /** 标签可选状态：1可选 0不可选 */
    @TableField("display_state")
    private Integer displayState;

    /** 操作人身份标识 */
    @TableField("user_identify")
    private String userIdentify;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField("updated_time")
    private LocalDateTime updatedTime;

    /** 打标方式 */
    @TableField("marking_method")
    private String markingMethod ;
}
