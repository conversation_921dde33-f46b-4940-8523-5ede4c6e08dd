package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * admin小结与问题映射表（临时）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10 03:01:33
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("summary_question")
public class SummaryQuestion implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ("自增id")
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ("小结id")
     */
    @TableField("summary_id")
    private Long summaryId;

    /**
     * ("一级问题分类")
     */
    @TableField("question_lv1")
    private String questionLv1;

    /**
     * ("二级问题分类")
     */
    @TableField("question_lv2")
    private String questionLv2;
}
