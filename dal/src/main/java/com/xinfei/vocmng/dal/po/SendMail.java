/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ SendMail, v 0.1 2024/5/31 05:00 wancheng.qu Exp $
 */
@Data
@TableName("send_mail")
public class SendMail {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 发送人 */
    @TableField("sender")
    private String sender;

    /** 邮箱密码 */
    @TableField("epass")
    private String epass;

    /** 发送对象，1:资方，2:用户 */
    @TableField("send_object")
    private Integer sendObject;

    /** 发送邮箱 */
    @TableField("send_mail")
    private String sendMail;

    /** 抄送箱 */
    @TableField("cbox")
    private String cbox;

    /** 发送app */
    @TableField("send_app")
    private String sendApp;

    /** 创建人操作人身份标识 */
    @TableField("create_user")
    private String createUser;

    /** 修改人操作人身份标识 */
    @TableField("update_user")
    private String updateUser;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField(value = "updated_time", update = "CURRENT_TIMESTAMP")
    private LocalDateTime updatedTime;
}
