/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xinfei.vocmng.dal.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @version $ DocumentRecord, v 0.1 2024/5/31 04:58 wancheng.qu Exp $
 */
@Builder
@Data
@TableName("document_record")
public class DocumentRecord {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_no")
    private String userNo;

    /** 订单号 */
    @TableField("order_no")
    private String orderNo;

    /** 文件类型，1:信飞结清证明，2:资方结清证明，3:借款凭证 ，4:明珠结清证明，5:居间协议*/
    @TableField("type")
    private Integer type;

    /** 状态，1:已提交，2:已发送 */
    @TableField("status")
    private Integer status;

    /** 推送状态 1:已推送 2推送异常 3线下提交 */
    @TableField("access")
    private Integer access;

    /** 文件url */
    @TableField("file_url")
    private String fileUrl;

    /** 邮箱 */
    @TableField("mail")
    private String mail;

    /** 创建人操作人身份标识 */
    @TableField("create_user")
    private String createUser;

    /** 修改人操作人身份标识 */
    @TableField("update_user")
    private String updateUser;

    /** 是否删除：1已删除 0未删除 */
    @TableField("is_deleted")
    private Integer isDeleted;

    /** 资方 */
    @TableField("capital_pool")
    private String capitalPool;

    /** 本金 */
    @TableField("prin_amt")
    private BigDecimal prinAmt;

    /** 创建时间 */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /** 修改时间 */
    @TableField(value = "updated_time", update = "CURRENT_TIMESTAMP")
    private LocalDateTime updatedTime;
}
